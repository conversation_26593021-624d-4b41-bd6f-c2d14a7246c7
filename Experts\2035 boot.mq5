#include <Trade\Trade.mqh>

CTrade tradeInstance;

// === إعدادات الإدخال ===
input double RiskPercent      = 2.0;
input double StopLossPips     = 100;
input double TargetPips       = 100;
input int    TargetsCount     = 3;
input double FixedLotSize     = 0.1;
input ENUM_TIMEFRAMES TimeFrame = PERIOD_M15;
input bool   EnableLong       = true;
input bool   EnableShort      = true;
input int    CooldownMinutes  = 30; // ✅ مدة الانتظار بين الصفقات بالدقائق

// === متغيرات داخلية ===
ulong positionTicket = 0;
double lotRemaining = 0.0;
bool positionOpen = false;
datetime lastTradeTime = 0;
double lotStep = 0.0;
bool isAnalysisDone = false;
int analysisDirection = 0; // 1 = شراء, -1 = بيع

int OnInit()
{
    lotStep = FixedLotSize / TargetsCount;
    return INIT_SUCCEEDED;
}

bool IsCooldownFinished()
{
    return (TimeCurrent() - lastTradeTime) >= (CooldownMinutes * 60);
}

void StartMarketAnalysis()
{
    double maFastBuffer[], maSlowBuffer[];

    int maFastHandle = iMA(_Symbol, TimeFrame, 10, 0, MODE_EMA, PRICE_CLOSE);
    int maSlowHandle = iMA(_Symbol, TimeFrame, 20, 0, MODE_EMA, PRICE_CLOSE);

    if (maFastHandle == INVALID_HANDLE || maSlowHandle == INVALID_HANDLE)
    {
        Print("❌ خطأ في إنشاء المؤشرات.");
        return;
    }

    if (CopyBuffer(maFastHandle, 0, 0, 1, maFastBuffer) <= 0 ||
        CopyBuffer(maSlowHandle, 0, 0, 1, maSlowBuffer) <= 0)
    {
        Print("❌ خطأ في نسخ بيانات المؤشرات.");
        return;
    }

    double maFast = maFastBuffer[0];
    double maSlow = maSlowBuffer[0];

    if (maFast > maSlow && EnableLong)
        analysisDirection = 1;  // شراء
    else if (maFast < maSlow && EnableShort)
        analysisDirection = -1; // بيع
    else
        analysisDirection = 0;  // محايد - لا توجد إشارة قوية

    isAnalysisDone = true;

    IndicatorRelease(maFastHandle);
    IndicatorRelease(maSlowHandle);
}

void OpenTrade()
{
    if (PositionsTotal() >= 1 || !IsCooldownFinished() || !isAnalysisDone) return;

    double price, sl, tp;

    if (analysisDirection == 1) // شراء
    {
        price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
        sl = price - StopLossPips * _Point;
        tp = price + (TargetsCount * TargetPips) * _Point;
        if (tradeInstance.PositionOpen(_Symbol, ORDER_TYPE_BUY, FixedLotSize, price, sl, tp))
        {
            lastTradeTime = TimeCurrent();
            isAnalysisDone = false; // ✅ إعادة التحليل للصفقة القادمة
            Print("✅ تم فتح صفقة شراء.");
        }
    }
    else if (analysisDirection == -1) // بيع
    {
        price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
        sl = price + StopLossPips * _Point;
        tp = price - (TargetsCount * TargetPips) * _Point;
        if (tradeInstance.PositionOpen(_Symbol, ORDER_TYPE_SELL, FixedLotSize, price, sl, tp))
        {
            lastTradeTime = TimeCurrent();
            isAnalysisDone = false;
            Print("✅ تم فتح صفقة بيع.");
        }
    }
}

void ManageTrade()
{
    if(!PositionSelect(_Symbol))
    {
        if (positionOpen)
        {
            lastTradeTime = TimeCurrent();
            Print("🚨 تم إغلاق الصفقة - بدء وقت الانتظار Cooldown");
        }
        positionOpen = false;
        lotRemaining = 0.0;
        positionTicket = 0;
        return;
    }

    positionOpen = true;
    positionTicket = PositionGetInteger(POSITION_TICKET);
    lotRemaining = PositionGetDouble(POSITION_VOLUME);

    double entryPrice = PositionGetDouble(POSITION_PRICE_OPEN);
    long type = PositionGetInteger(POSITION_TYPE);
    double currentPrice = (type == POSITION_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_BID) : SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double pointSize = SymbolInfoDouble(_Symbol, SYMBOL_POINT);

    double target1 = entryPrice + ((type == POSITION_TYPE_BUY) ? 50 * pointSize : -50 * pointSize);
    double target2 = entryPrice + ((type == POSITION_TYPE_BUY) ? 2 * TargetPips * pointSize : -2 * TargetPips * pointSize);
    double target3 = entryPrice + ((type == POSITION_TYPE_BUY) ? 3 * TargetPips * pointSize : -3 * TargetPips * pointSize);

    double sl = PositionGetDouble(POSITION_SL);

    if (lotRemaining > lotStep && ((type == POSITION_TYPE_BUY && currentPrice >= target1) || (type == POSITION_TYPE_SELL && currentPrice <= target1)))
    {
        if(tradeInstance.PositionClosePartial(positionTicket, lotStep))
        {
            lotRemaining -= lotStep;
            double newSL = entryPrice;
            if ((type == POSITION_TYPE_BUY && sl < newSL) || (type == POSITION_TYPE_SELL && sl > newSL))
                tradeInstance.PositionModify(_Symbol, newSL, PositionGetDouble(POSITION_TP));
        }
    }

    if (lotRemaining > lotStep && ((type == POSITION_TYPE_BUY && currentPrice >= target2) || (type == POSITION_TYPE_SELL && currentPrice <= target2)))
    {
        if(tradeInstance.PositionClosePartial(positionTicket, lotStep))
        {
            lotRemaining -= lotStep;
            double newSL = target1;
            if ((type == POSITION_TYPE_BUY && sl < newSL) || (type == POSITION_TYPE_SELL && sl > newSL))
                tradeInstance.PositionModify(_Symbol, newSL, PositionGetDouble(POSITION_TP));
        }
    }

    if (lotRemaining > 0 && ((type == POSITION_TYPE_BUY && currentPrice >= target3) || (type == POSITION_TYPE_SELL && currentPrice <= target3)))
    {
        if(tradeInstance.PositionClosePartial(positionTicket, lotRemaining))
        {
            lotRemaining = 0;
            positionOpen = false;
            lastTradeTime = TimeCurrent();
            Print("🎯 الصفقة وصلت للهدف الأخير - بدء Cooldown");
        }
    }
}

void OnTick()
{
    ManageTrade();

    if (!positionOpen && IsCooldownFinished() && !isAnalysisDone)
        StartMarketAnalysis(); // ✅ إجراء تحليل السوق أولاً

    if (!positionOpen && IsCooldownFinished() && isAnalysisDone)
        OpenTrade();           // ✅ ثم تنفيذ الصفقة بناءً على التحليل
}
