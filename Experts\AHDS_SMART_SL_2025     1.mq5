//+------------------------------------------------------------------+
//|     AHDS SMART STOP LOSS 2025 - Enhanced Stop Loss Logic        |
//|   🛡️ ستوب لوس ذكي يميز بين التصحيح والانعكاس الحقيقي 🛡️        |
//|   ✅ تم إصلاح مشكلة الستوب القريب والإغلاق المبكر ✅            |
//+------------------------------------------------------------------+
#property strict
#include <Trade\Trade.mqh>
CTrade tradeInstance;

//=============== إعدادات التداول الأساسية ==================
input double RiskPercent = 2.0;
input double TargetPips_TP1 = 150;
input double TargetPips_TP2 = 300;
input double TargetPips_TP3 = 500;
input double SL_OffsetPips_AfterTP1 = 20;
input double FixedLotSize = 0.04;               // 🔧 تم تغيير اللوت للتقسيم الأفضل
input double LotPercent_TP1 = 0.33;
input double LotPercent_TP2 = 0.5;
input double LotPercent_TP3 = 1.0;
input ENUM_TIMEFRAMES TimeFrame = PERIOD_M15;
input bool EnableLong = true;
input bool EnableShort = true;
input int CooldownMinutes = 30;
input bool ManualClose = false;

//=============== إعدادات التأكيد المحسنة ==================
input int confirmRequired = 8;                  // 🔧 تقليل التأكيدات (من 12 إلى 8)
input int confirmPauseMS = 3000;               // 🔧 تقليل الانتظار (من 5 إلى 3 ثواني)
input double MinEMAGapPoints = 15;             // 🔧 تقليل الفجوة المطلوبة
input double MinATRPoints = 10;                // 🔧 تقليل التقلبات المطلوبة
input bool UseReversalCandles = true;
input bool RequireOrderBlock = false;          // 🔧 إلغاء شرط Order Block
input bool EnableAlerts = true;

//=============== إعدادات الستوب الذكي الجديدة ==================
input group "=== Smart Stop Loss Settings ==="
input double ATR_Multiplier = 3.0;             // 🔧 زيادة كبيرة (من 1.5 إلى 3.0)
input double MinStopLossPips = 100;            // 🔧 زيادة كبيرة (من 15 إلى 100)
input double MaxStopLossPips = 300;            // 🔧 حد أقصى جديد
input int SwingSearchRange = 50;               // 🔧 زيادة نطاق البحث (من 30 إلى 50)
input int StrengthAnalysisRange = 7;           // 🔧 زيادة نطاق تحليل القوة (من 3 إلى 7)
input double TrendStrengthMultiplier = 0.8;    // 🔧 مضاعف قوة الترند
input double CorrectionAllowance = 0.5;        // 🔧 نسبة التصحيح المسموحة (50%)
input bool UseAdvancedSL = true;               // 🔧 تفعيل الستوب المتقدم

//=============== إعدادات RSI المحسنة ==================
input double MinRSIStrength = 55;              // 🔧 تقليل شرط RSI للشراء
input double MaxRSIStrength = 45;              // 🔧 تقليل شرط RSI للبيع
input int MinCandleStrength = 2;               // 🔧 تقليل الشموع المطلوبة

enum ENUM_SL_METHOD { SL_SMART_SWING = 0, SL_SIMPLE = 1 };
input ENUM_SL_METHOD StopLossMethod = SL_SMART_SWING;

//=============== المتغيرات العامة ==================
int maFastHandle, maSlowHandle, rsiHandle, atrHandle;
ulong positionTicket = 0;
double lotRemaining = 0.0;
bool positionOpen = false;
datetime lastTradeTime = 0;
double lastAdjustedSL = 0.0;
double entryPrice = 0.0;
int positionDirection = 0;

// متغيرات الستوب الذكي
double lastSwingHigh = 0.0;
double lastSwingLow = 0.0;
double trendStrength = 0.0;
double correctionSize = 0.0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    maFastHandle = iMA(_Symbol, TimeFrame, 10, 0, MODE_EMA, PRICE_CLOSE);
    maSlowHandle = iMA(_Symbol, TimeFrame, 20, 0, MODE_EMA, PRICE_CLOSE);
    rsiHandle = iRSI(_Symbol, TimeFrame, 14, PRICE_CLOSE);
    atrHandle = iATR(_Symbol, TimeFrame, 14);

    if (maFastHandle == INVALID_HANDLE || maSlowHandle == INVALID_HANDLE ||
        rsiHandle == INVALID_HANDLE || atrHandle == INVALID_HANDLE)
        return INIT_FAILED;

    Print("🛡️ ═══════════════════════════════════════");
    Print("🚀 تم تحميل AHDS SMART SL 2025 بنجاح!");
    Print("🧠 ستوب لوس ذكي مع حماية من التصحيحات");
    Print("🔧 إعدادات محسنة لتجنب الإغلاق المبكر");
    Print("🛡️ ═══════════════════════════════════════");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    IndicatorRelease(maFastHandle);
    IndicatorRelease(maSlowHandle);
    IndicatorRelease(rsiHandle);
    IndicatorRelease(atrHandle);
}

//+------------------------------------------------------------------+
//| دوال مساعدة                                                     |
//+------------------------------------------------------------------+
bool IsCooldownFinished() 
{ 
    return (TimeCurrent() - lastTradeTime) >= (CooldownMinutes * 60); 
}

double GetATRPips()
{
    double atrBuffer[];
    if (CopyBuffer(atrHandle, 0, 0, 1, atrBuffer) <= 0) return 0;
    return atrBuffer[0] / _Point;
}

void SendAlert(string msg) 
{ 
    if (EnableAlerts) Alert(msg); 
}

//+------------------------------------------------------------------+
//| حساب قوة الترند                                                 |
//+------------------------------------------------------------------+
double CalculateTrendStrength()
{
    double strength = 0.0;
    int bullishBars = 0, bearishBars = 0;
    
    // تحليل آخر 20 شمعة
    for(int i = 1; i <= 20; i++)
    {
        double open = iOpen(_Symbol, TimeFrame, i);
        double close = iClose(_Symbol, TimeFrame, i);
        
        if(close > open) bullishBars++;
        else if(close < open) bearishBars++;
    }
    
    // حساب قوة الترند (0 = متوازن، 1 = ترند قوي)
    strength = MathAbs(bullishBars - bearishBars) / 20.0;
    
    return strength;
}

//+------------------------------------------------------------------+
//| حساب حجم الحركة السابقة                                         |
//+------------------------------------------------------------------+
double CalculatePreviousMove(bool isBuy)
{
    double moveSize = 0.0;
    
    if(isBuy)
    {
        // للشراء: حساب حجم الهبوط السابق
        double highestPoint = iHigh(_Symbol, TimeFrame, 1);
        double lowestPoint = iLow(_Symbol, TimeFrame, 1);
        
        for(int i = 2; i <= 30; i++)
        {
            double high = iHigh(_Symbol, TimeFrame, i);
            double low = iLow(_Symbol, TimeFrame, i);
            
            if(high > highestPoint) highestPoint = high;
            if(low < lowestPoint) lowestPoint = low;
        }
        
        moveSize = highestPoint - lowestPoint;
    }
    else
    {
        // للبيع: حساب حجم الصعود السابق
        double highestPoint = iHigh(_Symbol, TimeFrame, 1);
        double lowestPoint = iLow(_Symbol, TimeFrame, 1);
        
        for(int i = 2; i <= 30; i++)
        {
            double high = iHigh(_Symbol, TimeFrame, i);
            double low = iLow(_Symbol, TimeFrame, i);
            
            if(high > highestPoint) highestPoint = high;
            if(low < lowestPoint) lowestPoint = low;
        }
        
        moveSize = highestPoint - lowestPoint;
    }
    
    return moveSize;
}

//+------------------------------------------------------------------+
//| الستوب الذكي المحسن - الدالة الرئيسية                          |
//+------------------------------------------------------------------+
double GenerateSmartStopLoss(bool isBuy, double priceEntry)
{
    if(!UseAdvancedSL || StopLossMethod != SL_SMART_SWING)
    {
        // الطريقة البسيطة
        double atrValue = GetATRPips();
        double simpleStop = atrValue * 2.0;
        if(simpleStop < MinStopLossPips) simpleStop = MinStopLossPips;
        if(simpleStop > MaxStopLossPips) simpleStop = MaxStopLossPips;
        
        return isBuy ? priceEntry - simpleStop * _Point : priceEntry + simpleStop * _Point;
    }
    
    Print("🧠 بدء حساب الستوب الذكي المتقدم...");
    
    // 1. البحث عن أقوى swing point
    double extremum;
    int bestIndex = 0;
    double bestStrength = 0;
    
    if(isBuy)
    {
        extremum = FindStrongestSwingLow(bestIndex, bestStrength);
        lastSwingLow = extremum;
    }
    else
    {
        extremum = FindStrongestSwingHigh(bestIndex, bestStrength);
        lastSwingHigh = extremum;
    }
    
    // 2. حساب قوة الترند
    trendStrength = CalculateTrendStrength();
    
    // 3. حساب حجم الحركة السابقة
    double previousMove = CalculatePreviousMove(isBuy);
    
    // 4. حساب التصحيح المسموح
    correctionSize = previousMove * CorrectionAllowance;
    
    // 5. حساب المسافة الأساسية
    double atrValue = GetATRPips();
    double baseBuffer = atrValue * ATR_Multiplier;
    
    // 6. تطبيق مضاعف قوة الترند
    double trendMultiplier = 1.0 + (trendStrength * TrendStrengthMultiplier);
    
    // 7. تطبيق مضاعف قوة النقطة
    double strengthMultiplier = 1.0 + (bestStrength * 0.3); // زيادة من 0.2 إلى 0.3
    
    // 8. حساب المسافة النهائية
    double finalBuffer = baseBuffer * trendMultiplier * strengthMultiplier;
    
    // 9. إضافة مسافة التصحيح
    finalBuffer = MathMax(finalBuffer, correctionSize / _Point);
    
    // 10. تطبيق الحدود
    if(finalBuffer < MinStopLossPips) finalBuffer = MinStopLossPips;
    if(finalBuffer > MaxStopLossPips) finalBuffer = MaxStopLossPips;
    
    // 11. حساب السعر النهائي
    double stopPrice = isBuy ? extremum - finalBuffer * _Point : extremum + finalBuffer * _Point;
    
    // طباعة التفاصيل
    Print("🛡️ ═══ تفاصيل الستوب الذكي ═══");
    Print("📍 ", (isBuy ? "أقوى قاع" : "أقوى قمة"), " في الشمعة ", bestIndex);
    Print("💪 قوة النقطة: ", bestStrength, " | قوة الترند: ", DoubleToString(trendStrength, 2));
    Print("📏 ATR: ", DoubleToString(atrValue, 1), " | الحركة السابقة: ", DoubleToString(previousMove/_Point, 1));
    Print("🔧 التصحيح المسموح: ", DoubleToString(correctionSize/_Point, 1), " نقطة");
    Print("🎯 المسافة النهائية: ", DoubleToString(finalBuffer, 1), " نقطة");
    Print("🛡️ ═══════════════════════════");
    
    return stopPrice;
}

//+------------------------------------------------------------------+
//| البحث عن أقوى قاع                                               |
//+------------------------------------------------------------------+
double FindStrongestSwingLow(int &bestIndex, double &bestStrength)
{
    double strongestLow = iLow(_Symbol, TimeFrame, 5); // بدء من شمعة أبعد
    bestIndex = 5;
    bestStrength = 0;

    for(int i = 5; i <= SwingSearchRange; i++) // نطاق أوسع
    {
        double currentLow = iLow(_Symbol, TimeFrame, i);

        // حساب قوة القاع بنطاق أوسع
        int strength = 0;
        for(int j = i - StrengthAnalysisRange; j <= i + StrengthAnalysisRange; j++)
        {
            if(j >= 0 && j != i && j <= Bars(_Symbol, TimeFrame))
            {
                double compareLow = iLow(_Symbol, TimeFrame, j);
                if(compareLow > currentLow + (2 * _Point)) strength++; // تساهل أكثر
            }
        }

        // اختيار أقوى قاع مع تفضيل الأقدم عند التساوي
        if(currentLow < strongestLow ||
           (MathAbs(currentLow - strongestLow) <= 5 * _Point && strength > bestStrength))
        {
            strongestLow = currentLow;
            bestIndex = i;
            bestStrength = strength;
        }
    }

    return strongestLow;
}

//+------------------------------------------------------------------+
//| البحث عن أقوى قمة                                               |
//+------------------------------------------------------------------+
double FindStrongestSwingHigh(int &bestIndex, double &bestStrength)
{
    double strongestHigh = iHigh(_Symbol, TimeFrame, 5); // بدء من شمعة أبعد
    bestIndex = 5;
    bestStrength = 0;

    for(int i = 5; i <= SwingSearchRange; i++) // نطاق أوسع
    {
        double currentHigh = iHigh(_Symbol, TimeFrame, i);

        // حساب قوة القمة بنطاق أوسع
        int strength = 0;
        for(int j = i - StrengthAnalysisRange; j <= i + StrengthAnalysisRange; j++)
        {
            if(j >= 0 && j != i && j <= Bars(_Symbol, TimeFrame))
            {
                double compareHigh = iHigh(_Symbol, TimeFrame, j);
                if(compareHigh < currentHigh - (2 * _Point)) strength++; // تساهل أكثر
            }
        }

        // اختيار أقوى قمة مع تفضيل الأقدم عند التساوي
        if(currentHigh > strongestHigh ||
           (MathAbs(currentHigh - strongestHigh) <= 5 * _Point && strength > bestStrength))
        {
            strongestHigh = currentHigh;
            bestIndex = i;
            bestStrength = strength;
        }
    }

    return strongestHigh;
}

//+------------------------------------------------------------------+
//| فحص شمعة الانعكاس المحسن                                        |
//+------------------------------------------------------------------+
bool IsReversalCandle(bool isBuy)
{
    double open = iOpen(_Symbol, TimeFrame, 1);
    double close = iClose(_Symbol, TimeFrame, 1);
    double high = iHigh(_Symbol, TimeFrame, 1);
    double low = iLow(_Symbol, TimeFrame, 1);
    double body = MathAbs(close - open);
    double wickUpper = high - MathMax(open, close);
    double wickLower = MathMin(open, close) - low;

    // شروط أكثر مرونة
    if (isBuy && wickLower > 1.2 * body && close >= open) // تقليل من 1.5 إلى 1.2
    {
        Print("🕯️ شمعة انعكاس للشراء: ذيل سفلي قوي");
        return true;
    }
    if (!isBuy && wickUpper > 1.2 * body && close <= open) // تقليل من 1.5 إلى 1.2
    {
        Print("🕯️ شمعة انعكاس للبيع: ذيل علوي قوي");
        return true;
    }

    return false;
}

//+------------------------------------------------------------------+
//| فحص منطقة Order Block المحسن                                   |
//+------------------------------------------------------------------+
bool IsOrderBlockZone(bool isBuy)
{
    double prevHigh = iHigh(_Symbol, TimeFrame, 1);
    double prevLow = iLow(_Symbol, TimeFrame, 1);
    double currentHigh = iHigh(_Symbol, TimeFrame, 0);
    double currentLow = iLow(_Symbol, TimeFrame, 0);
    double tolerance = 5 * _Point; // زيادة التساهل

    if (isBuy && currentLow <= prevLow + tolerance)
    {
        Print("📦 منطقة Order Block للشراء");
        return true;
    }
    if (!isBuy && currentHigh >= prevHigh - tolerance)
    {
        Print("📦 منطقة Order Block للبيع");
        return true;
    }

    return false;
}

//+------------------------------------------------------------------+
//| فتح الصفقة مع الستوب الذكي                                      |
//+------------------------------------------------------------------+
void OpenTrade(int direction)
{
    double price = (direction == 1) ? SymbolInfoDouble(_Symbol, SYMBOL_ASK) : SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double sl = GenerateSmartStopLoss(direction == 1, price);

    // حساب المسافة للتأكد
    double slDistance = MathAbs(price - sl) / _Point;

    Print("💰 سعر الدخول: ", DoubleToString(price, _Digits));
    Print("🛡️ الستوب الذكي: ", DoubleToString(sl, _Digits));
    Print("📏 مسافة الستوب: ", DoubleToString(slDistance, 1), " نقطة");

    bool result = (direction == 1) ? tradeInstance.Buy(FixedLotSize, _Symbol, price, sl, 0)
                                   : tradeInstance.Sell(FixedLotSize, _Symbol, price, sl, 0);

    if (result)
    {
        positionTicket = tradeInstance.ResultOrder();
        positionOpen = true;
        lotRemaining = FixedLotSize;
        lastAdjustedSL = sl;
        lastTradeTime = TimeCurrent();
        entryPrice = price;
        positionDirection = direction;

        string directionText = (direction == 1) ? "شراء" : "بيع";

        Print("✅ ═══════════════════════════════════════");
        Print("🚀 فتح صفقة ", directionText, " SMART SL");
        Print("💰 السعر: ", DoubleToString(price, _Digits));
        Print("🛡️ الستوب الذكي: ", DoubleToString(sl, _Digits));
        Print("📏 المسافة: ", DoubleToString(slDistance, 1), " نقطة");
        Print("🧠 قوة الترند: ", DoubleToString(trendStrength, 2));
        Print("✅ ═══════════════════════════════════════");

        SendAlert("✅ فتح صفقة " + directionText + " مع ستوب ذكي " + DoubleToString(slDistance, 0) + " نقطة");
    }
    else
    {
        Print("❌ فشل فتح الصفقة. الخطأ: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| تحليل اتجاه السوق المحسن                                        |
//+------------------------------------------------------------------+
int AnalyzeMarketDirection()
{
    double maFastBuffer[], maSlowBuffer[], rsiBuffer[];
    if (CopyBuffer(maFastHandle, 0, 0, 1, maFastBuffer) <= 0 ||
        CopyBuffer(maSlowHandle, 0, 0, 1, maSlowBuffer) <= 0 ||
        CopyBuffer(rsiHandle, 0, 0, 1, rsiBuffer) <= 0)
        return 0;

    double maFast = maFastBuffer[0], maSlow = maSlowBuffer[0], rsiValue = rsiBuffer[0];
    double emaGap = MathAbs(maFast - maSlow) / _Point;
    if (emaGap < MinEMAGapPoints) return 0;

    double atr = GetATRPips();
    if (atr < MinATRPoints) return 0;

    // تحليل انعكاس محسن مع شروط أكثر مرونة
    if (EnableLong && IsReversalToBuy())
    {
        Print("🔍 إشارة انعكاس للشراء مؤكدة");
        return 1;
    }

    if (EnableShort && IsReversalToSell())
    {
        Print("🔍 إشارة انعكاس للبيع مؤكدة");
        return -1;
    }

    return 0;
}

//+------------------------------------------------------------------+
//| تحليل الانعكاس للشراء المحسن                                    |
//+------------------------------------------------------------------+
bool IsReversalToBuy()
{
    // 1. فحص الترند الهابط السابق (مرن أكثر)
    bool wasDownTrend = false;
    int downTrendCount = 0;

    for(int i = 3; i <= 12; i++) // نطاق أقصر
    {
        double high1 = iHigh(_Symbol, TimeFrame, i);
        double high2 = iHigh(_Symbol, TimeFrame, i-3);
        if(high1 > high2) downTrendCount++;
    }

    wasDownTrend = (downTrendCount >= 2); // شرط أكثر مرونة

    if(!wasDownTrend)
    {
        Print("🔍 لا يوجد ترند هابط سابق واضح");
        return false;
    }

    // 2. البحث عن قاع حديث
    double recentLow = iLow(_Symbol, TimeFrame, 0);
    for(int i = 1; i <= 8; i++) // نطاق أقصر
    {
        double low = iLow(_Symbol, TimeFrame, i);
        if(low < recentLow) recentLow = low;
    }

    // 3. تأكيد بداية الانعكاس (مرن أكثر)
    double currentClose = iClose(_Symbol, TimeFrame, 0);
    double currentOpen = iOpen(_Symbol, TimeFrame, 0);
    double prevClose = iClose(_Symbol, TimeFrame, 1);

    bool bullishCandle = currentClose >= currentOpen; // تساهل أكثر
    bool aboveRecentLow = currentClose > recentLow + (5 * _Point); // تقليل المسافة
    bool momentum = currentClose >= prevClose; // تساهل أكثر

    // 4. تأكيد RSI محسن
    double rsiBuffer[];
    if(CopyBuffer(rsiHandle, 0, 0, 1, rsiBuffer) <= 0) return false;
    double rsiValue = rsiBuffer[0];
    bool rsiBullish = rsiValue > 40 && rsiValue < 75; // نطاق أوسع

    // 5. تأكيد المتوسطات محسن
    double maFastBuffer[], maSlowBuffer[];
    if(CopyBuffer(maFastHandle, 0, 0, 1, maFastBuffer) <= 0 ||
       CopyBuffer(maSlowHandle, 0, 0, 1, maSlowBuffer) <= 0) return false;

    double maFast = maFastBuffer[0], maSlow = maSlowBuffer[0];
    bool maRecovery = maFast >= maSlow - (10 * _Point); // تساهل أكثر

    Print("🔍 شراء: ترند هابط=", wasDownTrend, " | شمعة صاعدة=", bullishCandle,
          " | فوق القاع=", aboveRecentLow, " | RSI=", DoubleToString(rsiValue,1), " | MA=", maRecovery);

    return bullishCandle && aboveRecentLow && momentum && rsiBullish && maRecovery;
}

//+------------------------------------------------------------------+
//| تحليل الانعكاس للبيع المحسن                                     |
//+------------------------------------------------------------------+
bool IsReversalToSell()
{
    // 1. فحص الترند الصاعد السابق (مرن أكثر)
    bool wasUpTrend = false;
    int upTrendCount = 0;

    for(int i = 3; i <= 12; i++) // نطاق أقصر
    {
        double low1 = iLow(_Symbol, TimeFrame, i);
        double low2 = iLow(_Symbol, TimeFrame, i-3);
        if(low1 < low2) upTrendCount++;
    }

    wasUpTrend = (upTrendCount >= 2); // شرط أكثر مرونة

    if(!wasUpTrend)
    {
        Print("🔍 لا يوجد ترند صاعد سابق واضح");
        return false;
    }

    // 2. البحث عن قمة حديثة
    double recentHigh = iHigh(_Symbol, TimeFrame, 0);
    for(int i = 1; i <= 8; i++) // نطاق أقصر
    {
        double high = iHigh(_Symbol, TimeFrame, i);
        if(high > recentHigh) recentHigh = high;
    }

    // 3. تأكيد بداية الانعكاس (مرن أكثر)
    double currentClose = iClose(_Symbol, TimeFrame, 0);
    double currentOpen = iOpen(_Symbol, TimeFrame, 0);
    double prevClose = iClose(_Symbol, TimeFrame, 1);

    bool bearishCandle = currentClose <= currentOpen; // تساهل أكثر
    bool belowRecentHigh = currentClose < recentHigh - (5 * _Point); // تقليل المسافة
    bool momentum = currentClose <= prevClose; // تساهل أكثر

    // 4. تأكيد RSI محسن
    double rsiBuffer[];
    if(CopyBuffer(rsiHandle, 0, 0, 1, rsiBuffer) <= 0) return false;
    double rsiValue = rsiBuffer[0];
    bool rsiBearish = rsiValue < 60 && rsiValue > 25; // نطاق أوسع

    // 5. تأكيد المتوسطات محسن
    double maFastBuffer[], maSlowBuffer[];
    if(CopyBuffer(maFastHandle, 0, 0, 1, maFastBuffer) <= 0 ||
       CopyBuffer(maSlowHandle, 0, 0, 1, maSlowBuffer) <= 0) return false;

    double maFast = maFastBuffer[0], maSlow = maSlowBuffer[0];
    bool maWeakness = maFast <= maSlow + (10 * _Point); // تساهل أكثر

    Print("🔍 بيع: ترند صاعد=", wasUpTrend, " | شمعة هابطة=", bearishCandle,
          " | تحت القمة=", belowRecentHigh, " | RSI=", DoubleToString(rsiValue,1), " | MA=", maWeakness);

    return bearishCandle && belowRecentHigh && momentum && rsiBearish && maWeakness;
}

//+------------------------------------------------------------------+
//| انتظار وتأكيد اتجاه السوق                                       |
//+------------------------------------------------------------------+
void WaitAndConfirmMarketDirection()
{
    int confirmCounter = 0;
    int lastConfirmedDirection = 0;

    Print("🔍 بدء عملية التأكيد...");

    for (int i = 0; i < confirmRequired; i++)
    {
        int direction = AnalyzeMarketDirection();

        if (i == 0)
            lastConfirmedDirection = direction;
        else if (direction != lastConfirmedDirection || direction == 0)
        {
            Print("❗ التحليل غير متطابق أثناء التأكيد - إلغاء");
            return;
        }

        confirmCounter++;
        string dirText = (direction == 1) ? "شراء" : (direction == -1) ? "بيع" : "لا شيء";
        Print("✅ تأكيد ", confirmCounter, "/", confirmRequired, " للاتجاه: ", dirText);

        if(i < confirmRequired - 1)
            Sleep(confirmPauseMS);
    }

    if (confirmCounter == confirmRequired && lastConfirmedDirection != 0)
    {
        // فحص شروط إضافية
        bool reversalConfirm = !UseReversalCandles || IsReversalCandle(lastConfirmedDirection == 1);
        bool obConfirm = !RequireOrderBlock || IsOrderBlockZone(lastConfirmedDirection == 1);

        if (reversalConfirm && obConfirm)
        {
            string directionText = (lastConfirmedDirection == 1) ? "شراء" : "بيع";
            SendAlert("🚀 تأكيد " + directionText + " مع ستوب ذكي - جاري فتح الصفقة...");
            OpenTrade(lastConfirmedDirection);
        }
        else
        {
            Print("❗ فشل في الشروط الإضافية - إلغاء الصفقة");
        }
    }
}

//+------------------------------------------------------------------+
//| إدارة الصفقة المفتوحة                                           |
//+------------------------------------------------------------------+
void ManageOpenPosition()
{
    if (!positionOpen) return;
    if (!PositionSelect(_Symbol))
    {
        positionOpen = false;
        lotRemaining = 0.0;
        positionTicket = 0;
        positionDirection = 0;
        return;
    }

    double currentPrice = (positionDirection == 1) ?
        SymbolInfoDouble(_Symbol, SYMBOL_BID) : SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    // حساب أسعار الأهداف
    double tp1Price = (positionDirection == 1) ?
        entryPrice + TargetPips_TP1 * _Point : entryPrice - TargetPips_TP1 * _Point;
    double tp2Price = (positionDirection == 1) ?
        entryPrice + TargetPips_TP2 * _Point : entryPrice - TargetPips_TP2 * _Point;
    double tp3Price = (positionDirection == 1) ?
        entryPrice + TargetPips_TP3 * _Point : entryPrice - TargetPips_TP3 * _Point;

    ulong ticket = PositionGetInteger(POSITION_TICKET);
    double posLots = PositionGetDouble(POSITION_VOLUME);
    double slCurrent = PositionGetDouble(POSITION_SL);

    // إدارة الهدف الأول
    if (((positionDirection == 1) && currentPrice >= tp1Price) ||
        ((positionDirection == -1) && currentPrice <= tp1Price))
    {
        double lotToClose = posLots * LotPercent_TP1;
        if (lotToClose > 0 && lotRemaining > 0)
        {
            if (tradeInstance.PositionClosePartial(ticket, lotToClose))
            {
                lotRemaining -= lotToClose;
                Print("🎯 تحقيق TP1 - إغلاق جزئي: ", DoubleToString(lotToClose, 2), " لوت");
                SendAlert("✅ تحقيق الهدف الأول - إغلاق جزئي");
            }
        }

        // نقل الستوب إلى الدخول + أوفست
        double newSL = (positionDirection == 1) ?
            entryPrice + SL_OffsetPips_AfterTP1 * _Point :
            entryPrice - SL_OffsetPips_AfterTP1 * _Point;

        if ((positionDirection == 1 && newSL > slCurrent) ||
            (positionDirection == -1 && newSL < slCurrent))
        {
            if (tradeInstance.PositionModify(ticket, newSL, 0))
            {
                lastAdjustedSL = newSL;
                Print("✅ نقل الستوب إلى الدخول + ", SL_OffsetPips_AfterTP1, " نقطة");
                SendAlert("✅ تم نقل الستوب إلى الدخول بعد TP1");
            }
        }
    }

    // إدارة الهدف الثاني
    if (((positionDirection == 1) && currentPrice >= tp2Price) ||
        ((positionDirection == -1) && currentPrice <= tp2Price))
    {
        double lotToClose = posLots * LotPercent_TP2;
        if (lotToClose > 0 && lotRemaining > 0)
        {
            if (tradeInstance.PositionClosePartial(ticket, lotToClose))
            {
                lotRemaining -= lotToClose;
                Print("🎯 تحقيق TP2 - إغلاق جزئي: ", DoubleToString(lotToClose, 2), " لوت");
                SendAlert("✅ تحقيق الهدف الثاني - إغلاق جزئي");
            }
        }
    }

    // إدارة الهدف الثالث
    if (((positionDirection == 1) && currentPrice >= tp3Price) ||
        ((positionDirection == -1) && currentPrice <= tp3Price))
    {
        double lotToClose = lotRemaining;
        if (lotToClose > 0)
        {
            if (tradeInstance.PositionClosePartial(ticket, lotToClose))
            {
                lotRemaining -= lotToClose;
                positionOpen = false;
                Print("🎯 تحقيق TP3 - إغلاق كامل");
                SendAlert("✅ تحقيق الهدف الثالث - إغلاق كامل");
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // الإغلاق اليدوي
    if (ManualClose && PositionSelect(_Symbol))
    {
        tradeInstance.PositionClose(_Symbol);
        positionOpen = false;
        lotRemaining = 0;
        lastTradeTime = TimeCurrent();
        Print("🔧 إغلاق يدوي للصفقة");
        return;
    }

    // إدارة الصفقات المفتوحة
    if (positionOpen)
    {
        ManageOpenPosition();
    }
    else if (IsCooldownFinished())
    {
        // البحث عن فرص تداول جديدة
        WaitAndConfirmMarketDirection();
    }
}
