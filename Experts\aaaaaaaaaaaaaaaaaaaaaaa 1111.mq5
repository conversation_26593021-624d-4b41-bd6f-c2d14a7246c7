//+------------------------------------------------------------------+
//|           الخال سنايبر المحسن | Gold Sniper Pro Enhanced v8     |
//+------------------------------------------------------------------+
#property copyright "By Ha Trader - Enhanced Version"
#property version   "8.0"
#property strict

#include <Trade\Trade.mqh>
CTrade trade;

// ======= إعدادات المستخدم =======
input string    mySymbol          = "XAUUSD";
input double    lot1              = 0.01; // دخول أساسي
input double    lot2              = 0.02; // تعزيز أول
input double    lot3              = 0.03; // تعزيز ثاني
input double    lot4              = 0.04; // لوت الانعكاس (مجموع السابقة)
input int       levelsAboveBelow  = 15;
input double    levelDistance     = 5.0;
input double    mainLevel1        = 2.40;
input double    mainLevel2        = 7.40;
input bool      EnableBuy         = true;
input bool      EnableSell        = true;
input bool      EnablePending     = true;
input bool      EnableMarket      = true;
input bool      EnableDebugPrint  = true;

// ======= متغيرات الدورة الموحدة =======
double entry_prices[4];
int entry_count = 0;
double first_entry = 0.0;
int tradeDirection = 0; // 0 = لا يوجد، 1 = شراء، -1 = بيع
bool inCycle = false;
datetime dayStartTime = 0;
bool drawnLevelsToday = false;

// ======= متغيرات الدورة العكسية =======
double reverse_entry_prices[4];
int reverse_entry_count = 0;
double reverse_first_entry = 0.0;
int reverseDirection = 0;

// ======= نوع الدورة =======
enum CycleType { NATURAL, REVERSE };
CycleType currentCycle = NATURAL;
int cycleNumber = 1;

//+------------------------------------------------------------------+
//| دالة حساب السبريد بالدقة                                         |
//+------------------------------------------------------------------+
double GetSpread()
{
    double ask = SymbolInfoDouble(mySymbol, SYMBOL_ASK);
    double bid = SymbolInfoDouble(mySymbol, SYMBOL_BID);
    return NormalizeDouble(ask - bid, _Digits);
}

//+------------------------------------------------------------------+
//| رسم المستويات على الشارت                                        |
//+------------------------------------------------------------------+
void DrawLevels()
{
    double price = SymbolInfoDouble(mySymbol, SYMBOL_BID);
    double basePrice = NormalizeDouble(price, 2);

    // حذف المستويات القديمة
    int total = ObjectsTotal(0, 0);
    for(int i=total-1; i>=0; i--)
    {
        string name = ObjectName(0, i, 0);
        if(StringFind(name, "SniperLevel") == 0)
            ObjectDelete(0, name);
    }

    // رسم المستويات الجديدة
    for(int d=-levelsAboveBelow; d<=levelsAboveBelow; d++)
    {
        double lvls[2] = {mainLevel1, mainLevel2};
        for(int k=0; k<2; k++)
        {
            double lvl = MathFloor(basePrice/10)*10 + lvls[k] + d*levelDistance;
            string name = "SniperLevel_" + DoubleToString(lvl,2);
            if(ObjectCreate(0, name, OBJ_HLINE, 0, 0, lvl))
            {
                ObjectSetInteger(0, name, OBJPROP_COLOR, clrOrange);
                ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
                ObjectSetInteger(0, name, OBJPROP_STYLE, STYLE_DOT);
            }
        }
    }
    drawnLevelsToday = true;
    if(EnableDebugPrint) Print("✅ تم رسم المستويات للسعر الحالي: ", basePrice);
}

//+------------------------------------------------------------------+
//| التحقق من أن السعر هو مستوى رئيسي (2.40 أو 7.40)                |
//+------------------------------------------------------------------+
bool IsMainLevel(double price)
{
    double x = NormalizeDouble(price, 2);
    double dec = x - MathFloor(x/10)*10;
    return (MathAbs(dec - mainLevel1) < 0.01 || MathAbs(dec - mainLevel2) < 0.01);
}

//+------------------------------------------------------------------+
//| التحقق من إمكانية الدخول على مستوى رئيسي                        |
//+------------------------------------------------------------------+
bool IsEligibleForEntry(int direction)
{
    double currentBid = SymbolInfoDouble(mySymbol, SYMBOL_BID);
    double currentAsk = SymbolInfoDouble(mySymbol, SYMBOL_ASK);
    
    double level = 0.0;
    if(direction == 1) // شراء
        level = NormalizeDouble(currentAsk, 2);
    else if(direction == -1) // بيع
        level = NormalizeDouble(currentBid, 2);
    else
        return false;

    // فحص شروط الدخول
    bool isMainLevelCheck = IsMainLevel(level);
    bool isExpectedLevelCheck = IsExpectedLevel(level);
    bool orderExistCheck = IsOrderExist(level, direction);
    bool alreadyEnhancedCheck = AlreadyEnhanced(level);
    
    bool eligible = (isMainLevelCheck && isExpectedLevelCheck && 
                    !orderExistCheck && !alreadyEnhancedCheck);
    
    if(EnableDebugPrint && isMainLevelCheck)
    {
        Print("🎯 فحص الأهلية للمستوى: ", level, " | الاتجاه: ", (direction==1?"شراء":"بيع"));
        Print("   ├─ مستوى رئيسي: ", (isMainLevelCheck?"✅":"❌"));
        Print("   ├─ مستوى متوقع: ", (isExpectedLevelCheck?"✅":"❌"));
        Print("   ├─ لا يوجد أمر: ", (!orderExistCheck?"✅":"❌"));
        Print("   ├─ لم يتم التعزيز: ", (!alreadyEnhancedCheck?"✅":"❌"));
        Print("   └─ النتيجة النهائية: ", (eligible?"✅ مؤهل":"❌ غير مؤهل"));
    }
    
    return eligible;
}

//+------------------------------------------------------------------+
//| التحقق من وجود أمر على نفس المستوى                             |
//+------------------------------------------------------------------+
bool IsOrderExist(double level, int dir)
{
    // فحص الصفقات المفتوحة
    for(int i=0; i<PositionsTotal(); i++)
    {
        ulong ticket = PositionGetTicket(i);
        if(PositionGetString(POSITION_SYMBOL) == mySymbol &&
           PositionGetInteger(POSITION_TYPE) == (dir==1?POSITION_TYPE_BUY:POSITION_TYPE_SELL))
        {
            double price = PositionGetDouble(POSITION_PRICE_OPEN);
            if(MathAbs(price-level) < 0.01)
                return true;
        }
    }
    
    // فحص الأوامر المعلقة
    for(int i=0; i<OrdersTotal(); i++)
    {
        ulong ticket = OrderGetTicket(i);
        if(OrderGetString(ORDER_SYMBOL) == mySymbol)
        {
            long orderType = OrderGetInteger(ORDER_TYPE);
            if((dir==1 && (orderType==ORDER_TYPE_BUY_LIMIT || orderType==ORDER_TYPE_BUY_STOP)) ||
               (dir==-1 && (orderType==ORDER_TYPE_SELL_LIMIT || orderType==ORDER_TYPE_SELL_STOP)))
            {
                double price = OrderGetDouble(ORDER_PRICE_OPEN);
                if(MathAbs(price-level) < 0.01)
                    return true;
            }
        }
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| التحقق إذا السعر مساوي بالضبط لمستوى تعزيز مطلوب                |
//+------------------------------------------------------------------+
bool IsExpectedLevel(double price)
{
    if(currentCycle == NATURAL)
    {
        if(entry_count == 0)
        {
            if(EnableDebugPrint)
                Print("📍 أول دخول - مسموح على أي مستوى رئيسي");
            return true;
        }
        
        double expected = first_entry - tradeDirection * levelDistance * entry_count;
        expected = NormalizeDouble(expected, 2);
        price = NormalizeDouble(price, 2);
        
        bool isExpected = (MathAbs(price - expected) <= 0.02);
        
        if(EnableDebugPrint)
        {
            Print("🔍 فحص المستوى المتوقع:");
            Print("   ├─ السعر الحالي: ", price);
            Print("   ├─ المستوى المتوقع: ", expected);
            Print("   ├─ الفرق: ", MathAbs(price - expected));
            Print("   └─ النتيجة: ", (isExpected?"✅ متوقع":"❌ غير متوقع"));
        }
        
        return isExpected;
    }
    else // REVERSE cycle
    {
        if(reverse_entry_count == 0)
        {
            if(EnableDebugPrint)
                Print("📍 أول دخول عكسي - مسموح على أي مستوى رئيسي");
            return true;
        }
        
        double expected = reverse_first_entry - reverseDirection * levelDistance * reverse_entry_count;
        expected = NormalizeDouble(expected, 2);
        price = NormalizeDouble(price, 2);
        
        bool isExpected = (MathAbs(price - expected) <= 0.02);
        
        if(EnableDebugPrint && !isExpected)
        {
            Print("🔍 فحص المستوى العكسي المتوقع:");
            Print("   ├─ السعر الحالي: ", price);
            Print("   ├─ المستوى المتوقع: ", expected);
            Print("   └─ الفرق: ", MathAbs(price - expected));
        }
        
        return isExpected;
    }
}

//+------------------------------------------------------------------+
//| التحقق هل المستوى تم الدخول عنده بالفعل                          |
//+------------------------------------------------------------------+
bool AlreadyEnhanced(double level)
{
    if(currentCycle == NATURAL)
    {
        for(int i=0; i<entry_count; i++)
            if(MathAbs(entry_prices[i] - level) < 0.01)
                return true;
    }
    else
    {
        for(int i=0; i<reverse_entry_count; i++)
            if(MathAbs(reverse_entry_prices[i] - level) < 0.01)
                return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| كشف الاتجاه وبدء الدورة                                         |
//+------------------------------------------------------------------+
void DetectDirection()
{
    if(tradeDirection != 0 || inCycle)
        return;
        
    double price = NormalizeDouble(SymbolInfoDouble(mySymbol, SYMBOL_BID),2);

    if(IsMainLevel(price))
    {
        if(EnableBuy && EnableSell)
        {
            tradeDirection = (MathRand() % 2 == 0) ? 1 : -1;
        }
        else if(EnableBuy)
            tradeDirection = 1;
        else if(EnableSell)
            tradeDirection = -1;
        else
            return;

        first_entry = price;
        entry_count = 0;
        inCycle = true;
        currentCycle = NATURAL;
        
        for(int i=0; i<4; i++)
            entry_prices[i] = 0.0;
        
        if(EnableDebugPrint)
            Print("🚀 بدء دورة جديدة #", cycleNumber, " | النوع: طبيعية", 
                  " | الاتجاه: ", (tradeDirection==1?"شراء":"بيع"), 
                  " | المستوى: ", price);
    }
}

//+------------------------------------------------------------------+
//| إدارة الدورة الحالية                                            |
//+------------------------------------------------------------------+
void ManageCycle()
{
    if(!inCycle || tradeDirection == 0)
        return;

    // محاولة الدخول إذا كان مؤهلاً (حد أقصى 3 تعزيزات)
    if(currentCycle == NATURAL && entry_count < 3 && IsEligibleForEntry(tradeDirection))
    {
        ExecuteEntry();
    }
    else if(currentCycle == REVERSE && reverse_entry_count < 4 && IsEligibleForEntry(reverseDirection))
    {
        ExecuteReverseEntry();
    }

    // إدارة الأهداف
    ManageTargets();
}

//+------------------------------------------------------------------+
//| تنفيذ الدخول الطبيعي                                            |
//+------------------------------------------------------------------+
void ExecuteEntry()
{
    double currentBid = SymbolInfoDouble(mySymbol, SYMBOL_BID);
    double currentAsk = SymbolInfoDouble(mySymbol, SYMBOL_ASK);
    
    double level = 0.0;
    if(tradeDirection == 1) // شراء
        level = NormalizeDouble(currentAsk, 2);
    else // بيع
        level = NormalizeDouble(currentBid, 2);

    // تحديد حجم اللوت حسب عدد التعزيزات
    double lot = lot1;
    if(entry_count == 1) lot = lot2;
    else if(entry_count == 2) lot = lot3;

    if(EnableDebugPrint)
        Print("🚀 محاولة فتح صفقة #", (entry_count + 1), " | المستوى: ", level, 
              " | اللوت: ", lot, " | النوع: ", (tradeDirection==1?"شراء":"بيع"));

    bool success = false;
    
    if(EnableMarket)
    {
        if(tradeDirection == 1)
            success = trade.Buy(lot, mySymbol);
        else
            success = trade.Sell(lot, mySymbol);
    }
    else if(EnablePending)
    {
        if(tradeDirection == 1)
            success = trade.BuyLimit(lot, level, mySymbol);
        else
            success = trade.SellLimit(lot, level, mySymbol);
    }

    if(success)
    {
        entry_prices[entry_count] = level;
        entry_count++;
        
        if(EnableDebugPrint)
            Print("✅ تم فتح الصفقة بنجاح #", entry_count, 
                  " | المستوى: ", level, " | الحجم: ", lot);
        
        // فحص الانعكاس فور فتح التعزيز الثالث
        if(entry_count == 3)
        {
            if(EnableDebugPrint)
                Print("🔄 تم الوصول للتعزيز الثالث - بدء الانعكاس");
                
            double totalLots = lot1 + lot2 + lot3;
            CloseAllPositions(tradeDirection);
            
            reverseDirection = -tradeDirection;
            bool reverseSuccess = false;
            if(reverseDirection == 1)
                reverseSuccess = trade.Buy(totalLots, mySymbol);
            else
                reverseSuccess = trade.Sell(totalLots, mySymbol);

            if(reverseSuccess)
            {
                currentCycle = REVERSE;
                reverse_first_entry = level;
                reverse_entry_count = 1;
                reverse_entry_prices[0] = level;
                
                for(int i=1; i<4; i++)
                    reverse_entry_prices[i] = 0.0;
                
                cycleNumber++;
                
                if(EnableDebugPrint)
                    Print("✅ تم الانعكاس بنجاح | اللوت العكسي: ", totalLots, 
                          " | الاتجاه الجديد: ", (reverseDirection==1?"شراء":"بيع"));
            }
            else
            {
                if(EnableDebugPrint)
                    Print("❌ فشل الانعكاس | الخطأ: ", trade.ResultRetcode());
            }
        }
    }
    else
    {
        if(EnableDebugPrint)
            Print("❌ فشل في فتح الصفقة | المستوى: ", level, 
                  " | الخطأ: ", trade.ResultRetcode());
    }
}

//+------------------------------------------------------------------+
//| تنفيذ الدخول العكسي                                             |
//+------------------------------------------------------------------+
void ExecuteReverseEntry()
{
    double currentBid = SymbolInfoDouble(mySymbol, SYMBOL_BID);
    double currentAsk = SymbolInfoDouble(mySymbol, SYMBOL_ASK);
    
    double level = 0.0;
    if(reverseDirection == 1)
        level = NormalizeDouble(currentAsk, 2);
    else
        level = NormalizeDouble(currentBid, 2);

    double lot = lot1;
    if(reverse_entry_count == 1) lot = lot2;
    else if(reverse_entry_count == 2) lot = lot3;
    else if(reverse_entry_count == 3) lot = lot4;

    bool success = false;
    if(EnableMarket)
    {
        if(reverseDirection == 1)
            success = trade.Buy(lot, mySymbol);
        else
            success = trade.Sell(lot, mySymbol);
    }

    if(success)
    {
        reverse_entry_prices[reverse_entry_count] = level;
        reverse_entry_count++;
        
        if(EnableDebugPrint)
            Print("✅ دخول عكسي #", reverse_entry_count, " | المستوى: ", level, 
                  " | الحجم: ", lot, " | النوع: ", (reverseDirection==1?"شراء":"بيع"));
    }
}

//+------------------------------------------------------------------+
//| إدارة الأهداف والإغلاق                                          |
//+------------------------------------------------------------------+
void ManageTargets()
{
    if(currentCycle == NATURAL && entry_count == 0)
        return;
    if(currentCycle == REVERSE && reverse_entry_count == 0)
        return;

    double lastEntry = (currentCycle == NATURAL) ? entry_prices[entry_count-1] : reverse_entry_prices[reverse_entry_count-1];
    double currentPrice = (currentCycle == NATURAL) ? 
                         ((tradeDirection == 1) ? SymbolInfoDouble(mySymbol, SYMBOL_BID) : SymbolInfoDouble(mySymbol, SYMBOL_ASK)) :
                         ((reverseDirection == 1) ? SymbolInfoDouble(mySymbol, SYMBOL_BID) : SymbolInfoDouble(mySymbol, SYMBOL_ASK));

    // منطق الهدف للدورة الطبيعية (لغاية التعزيز الثاني فقط)
    if(currentCycle == NATURAL && entry_count > 0 && entry_count < 3)
    {
        double targetLevel = CalculateTargetLevel(lastEntry, tradeDirection);
        
        bool shouldClose = false;
        if(tradeDirection == 1 && currentPrice >= targetLevel)
            shouldClose = true;
        if(tradeDirection == -1 && currentPrice <= targetLevel)
            shouldClose = true;

        if(shouldClose)
        {
            CloseAllPositions(tradeDirection);
            StartNewCycleAtTarget(targetLevel);
            return;
        }
    }

    // إدارة الدورة العكسية
    if(currentCycle == REVERSE && reverse_entry_count > 0)
    {
        double reverseTargetLevel = CalculateTargetLevel(lastEntry, reverseDirection);
        
        bool shouldCloseReverse = false;
        if(reverseDirection == 1 && currentPrice >= reverseTargetLevel)
            shouldCloseReverse = true;
        if(reverseDirection == -1 && currentPrice <= reverseTargetLevel)
            shouldCloseReverse = true;

        if(shouldCloseReverse)
        {
            CloseAllPositions(reverseDirection);
            CompleteFullCycle();
            return;
        }
    }
}

//+------------------------------------------------------------------+
//| حساب مستوى الهدف                                               |
//+------------------------------------------------------------------+
double CalculateTargetLevel(double entryLevel, int direction)
{
    double targetLevel = 0.0;
    
    if(direction == 1)
    {
        double lvl = entryLevel + levelDistance;
        while(!IsMainLevel(lvl) && lvl < entryLevel + 10*levelDistance)
            lvl += levelDistance;
        targetLevel = lvl;
    }
    else if(direction == -1)
    {
        double lvl = entryLevel - levelDistance;
        while(!IsMainLevel(lvl) && lvl > entryLevel - 10*levelDistance)
            lvl -= levelDistance;
        targetLevel = lvl;
    }
    
    return targetLevel;
}

//+------------------------------------------------------------------+
//| بدء دورة جديدة عند الهدف                                        |
//+------------------------------------------------------------------+
void StartNewCycleAtTarget(double targetLevel)
{
    if(EnableDebugPrint)
        Print("🎯 تم الوصول للهدف عند: ", targetLevel);

    double lot = lot1;
    bool entered = false;
    
    if(EnableMarket)
    {
        if(tradeDirection == 1)
            entered = trade.Buy(lot, mySymbol);
        else if(tradeDirection == -1)
            entered = trade.Sell(lot, mySymbol);
    }
    
    if(!entered && EnablePending)
    {
        if(tradeDirection == 1)
            trade.BuyLimit(lot, targetLevel, mySymbol);
        else if(tradeDirection == -1)
            trade.SellLimit(lot, targetLevel, mySymbol);
    }

    ResetCycle();
    first_entry = targetLevel;
    entry_count = 1;
    entry_prices[0] = targetLevel;
    inCycle = true;
    
    if(EnableDebugPrint)
        Print("🔄 إعادة تهيئة الدورة عند المستوى: ", targetLevel);
}

//+------------------------------------------------------------------+
//| إكمال الدورة الكاملة                                            |
//+------------------------------------------------------------------+
void CompleteFullCycle()
{
    if(EnableDebugPrint)
        Print("🏁 تم إكمال الدورة الكاملة #", cycleNumber);

    ResetCycle();
    tradeDirection = 0;
    reverseDirection = 0;
    currentCycle = NATURAL;
    cycleNumber++;
    
    reverse_entry_count = 0;
    reverse_first_entry = 0.0;
    for(int i=0; i<4; i++)
        reverse_entry_prices[i] = 0.0;
}

//+------------------------------------------------------------------+
//| إعادة تعيين الدورة                                              |
//+------------------------------------------------------------------+
void ResetCycle()
{
    entry_count = 0;
    first_entry = 0.0;
    inCycle = false;
    
    for(int i=0; i<4; i++)
        entry_prices[i] = 0.0;
}

//+------------------------------------------------------------------+
//| إغلاق جميع الصفقات للاتجاه المحدد                               |
//+------------------------------------------------------------------+
void CloseAllPositions(int dir)
{
    int closed = 0;
    for(int i=PositionsTotal()-1; i>=0; i--)
    {
        if(PositionGetSymbol(i) == mySymbol)
        {
            long posType = PositionGetInteger(POSITION_TYPE);
            if((dir==1 && posType==POSITION_TYPE_BUY) ||
               (dir==-1 && posType==POSITION_TYPE_SELL))
            {
                if(trade.PositionClose(PositionGetTicket(i)))
                    closed++;
            }
        }
    }
    
    if(EnableDebugPrint && closed > 0)
        Print("🔒 تم إغلاق ", closed, " صفقة للاتجاه: ", (dir==1?"شراء":"بيع"));
}

//+------------------------------------------------------------------+
//| دالة التهيئة                                                    |
//+------------------------------------------------------------------+
int OnInit()
{
    dayStartTime = iTime(mySymbol, PERIOD_D1, 0);
    DrawLevels();
    
    ResetCycle();
    tradeDirection = 0;
    reverseDirection = 0;
    currentCycle = NATURAL;
    cycleNumber = 1;
    
    reverse_entry_count = 0;
    reverse_first_entry = 0.0;
    for(int i=0; i<4; i++)
        reverse_entry_prices[i] = 0.0;
    
    Print("🚀 الخال سنايبر المحسن v8.0 - تم التشغيل بنجاح");
    Print("📊 النظام: دخول + تعزيزين ثم انعكاس");
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| الدالة الرئيسية                                                |
//+------------------------------------------------------------------+
void OnTick()
{
    datetime today = iTime(mySymbol, PERIOD_D1, 0);
    if(today != dayStartTime)
    {
        dayStartTime = today;
        drawnLevelsToday = false;
        if(EnableDebugPrint) Print("📅 يوم جديد - إعادة رسم المستويات");
    }
    
    if(!drawnLevelsToday)
        DrawLevels();

    DetectDirection();
    ManageCycle();
}

//+------------------------------------------------------------------+
//| دالة إنهاء البرنامج                                             |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    if(EnableDebugPrint)
        Print("🛑 تم إيقاف الخال سنايبر المحسن | السبب: ", reason);
}
