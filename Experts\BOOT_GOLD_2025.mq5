//+------------------------------------------------------------------+
//|                    BOOT GOLD 2025 - Fixed Stop Loss             |
//|   🥇 حل مشكلة الستوب القريب - ستوب ذكي محسن للذهب 🥇          |
//|   ✅ إصلاح شامل لجميع مشاكل حساب المسافة والنقاط المرجعية ✅   |
//+------------------------------------------------------------------+
#property strict
#include <Trade\Trade.mqh>
CTrade tradeInstance;

//=============== إعدادات التداول الأساسية ==================
input double RiskPercent = 2.0;
input double TargetPips_TP1 = 150;
input double TargetPips_TP2 = 300;
input double TargetPips_TP3 = 500;
input double SL_OffsetPips_AfterTP1 = 20;
input double FixedLotSize = 0.04;
input double LotPercent_TP1 = 0.33;
input double LotPercent_TP2 = 0.5;
input double LotPercent_TP3 = 1.0;
input ENUM_TIMEFRAMES TimeFrame = PERIOD_M15;
input bool EnableLong = true;
input bool EnableShort = true;
input int CooldownMinutes = 30;
input bool ManualClose = false;

//=============== إعدادات التأكيد المحسنة ==================
input int confirmRequired = 5;
input int confirmPauseMS = 2000;
input double MinEMAGapPoints = 8;
input double MinATRPoints = 3;
input bool UseReversalCandles = true;
input bool RequireOrderBlock = false;
input bool EnableAlerts = true;

//=============== إعدادات الستوب الذكي المحسن ==================
input group "=== BOOT GOLD Smart Stop Loss Settings ==="
input bool EnableAutoDetection = true;
input double ATR_Multiplier_Forex = 3.0;
input double ATR_Multiplier_Gold = 6.0;
input double MinStopLossPips_Forex = 100;
input double MinStopLossPips_Gold = 1500;
input double MaxStopLossPips_Forex = 300;
input double MaxStopLossPips_Gold = 8000;
input int SwingSearchRange = 40;
input int StrengthAnalysisRange = 5;
input double TrendStrengthMultiplier = 0.8;
input double CorrectionAllowance = 0.4;
input bool UseAdvancedSL = true;

//=============== إصلاحات مشاكل الستوب ==================
input group "=== Stop Loss Problem Fixes ==="
input int MinSwingDistance = 10;               // 🔧 الحد الأدنى لبداية البحث (شموع)
input double GoldTolerancePips = 50;           // 🔧 التساهل للذهب (نقطة)
input double ForexTolerancePips = 10;          // 🔧 التساهل للفوركس (نقطة)
input double MinDistanceFromEntry = 500;       // 🔧 الحد الأدنى للمسافة من الدخول (نقطة)
input bool EnableDistanceCheck = true;         // 🔧 تفعيل فحص المسافة النهائية
input bool EnableDetailedLogging = true;       // 🔧 تفعيل التسجيل المفصل

//=============== إعدادات RSI المحسنة ==================
input double MinRSIStrength = 45;
input double MaxRSIStrength = 55;
input int MinCandleStrength = 2;

enum ENUM_SL_METHOD { SL_SMART_SWING = 0, SL_SIMPLE = 1 };
input ENUM_SL_METHOD StopLossMethod = SL_SMART_SWING;

enum ENUM_SYMBOL_TYPE { SYMBOL_FOREX = 0, SYMBOL_GOLD = 1, SYMBOL_INDEX = 2 };

//=============== المتغيرات العامة ==================
int maFastHandle, maSlowHandle, rsiHandle, atrHandle;
ulong positionTicket = 0;
double lotRemaining = 0.0;
bool positionOpen = false;
datetime lastTradeTime = 0;
double lastAdjustedSL = 0.0;
double entryPrice = 0.0;
int positionDirection = 0;

// متغيرات الستوب الذكي
double lastSwingHigh = 0.0;
double lastSwingLow = 0.0;
double trendStrength = 0.0;
double correctionSize = 0.0;

// متغيرات كشف الرمز
ENUM_SYMBOL_TYPE symbolType = SYMBOL_FOREX;
double currentATRMultiplier = 3.0;
double currentMinStopPips = 100;
double currentMaxStopPips = 300;
double currentTolerancePips = 10;
string symbolName = "";

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // كشف نوع الرمز تلقائياً
    DetectSymbolType();
    
    maFastHandle = iMA(_Symbol, TimeFrame, 10, 0, MODE_EMA, PRICE_CLOSE);
    maSlowHandle = iMA(_Symbol, TimeFrame, 20, 0, MODE_EMA, PRICE_CLOSE);
    rsiHandle = iRSI(_Symbol, TimeFrame, 14, PRICE_CLOSE);
    atrHandle = iATR(_Symbol, TimeFrame, 14);

    if (maFastHandle == INVALID_HANDLE || maSlowHandle == INVALID_HANDLE ||
        rsiHandle == INVALID_HANDLE || atrHandle == INVALID_HANDLE)
        return INIT_FAILED;

    Print("🥇 ═══════════════════════════════════════");
    Print("🚀 تم تحميل BOOT GOLD 2025!");
    Print("🔧 إصلاح شامل لمشاكل الستوب لوس");
    Print("🔍 نوع الرمز المكتشف: ", GetSymbolTypeName());
    Print("🛡️ ستوب ذكي محسن مع فحص المسافة");
    Print("⚙️ حماية من النقاط المرجعية القريبة");
    Print("🥇 ═══════════════════════════════════════");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    IndicatorRelease(maFastHandle);
    IndicatorRelease(maSlowHandle);
    IndicatorRelease(rsiHandle);
    IndicatorRelease(atrHandle);
}

//+------------------------------------------------------------------+
//| كشف نوع الرمز تلقائياً                                          |
//+------------------------------------------------------------------+
void DetectSymbolType()
{
    symbolName = _Symbol;
    
    if(!EnableAutoDetection)
    {
        symbolType = SYMBOL_FOREX;
        SetForexSettings();
        return;
    }
    
    // كشف الذهب والمعادن الثمينة
    if(StringFind(symbolName, "XAU") >= 0 || 
       StringFind(symbolName, "GOLD") >= 0 ||
       StringFind(symbolName, "Au") >= 0 ||
       StringFind(symbolName, "XAG") >= 0 ||
       StringFind(symbolName, "SILVER") >= 0 ||
       StringFind(symbolName, "XPD") >= 0 ||
       StringFind(symbolName, "XPT") >= 0)
    {
        symbolType = SYMBOL_GOLD;
        SetGoldSettings();
        Print("🥇 تم كشف معدن ثمين: ", symbolName);
        return;
    }
    
    // كشف المؤشرات
    if(StringFind(symbolName, "DAX") >= 0 ||
       StringFind(symbolName, "SPX") >= 0 ||
       StringFind(symbolName, "NAS") >= 0 ||
       StringFind(symbolName, "DJI") >= 0 ||
       StringFind(symbolName, "FTSE") >= 0)
    {
        symbolType = SYMBOL_INDEX;
        SetIndexSettings();
        Print("📊 تم كشف مؤشر: ", symbolName);
        return;
    }
    
    // افتراضي: فوركس
    symbolType = SYMBOL_FOREX;
    SetForexSettings();
    Print("💱 رمز فوركس: ", symbolName);
}

//+------------------------------------------------------------------+
//| تطبيق إعدادات الفوركس                                          |
//+------------------------------------------------------------------+
void SetForexSettings()
{
    currentATRMultiplier = ATR_Multiplier_Forex;
    currentMinStopPips = MinStopLossPips_Forex;
    currentMaxStopPips = MaxStopLossPips_Forex;
    currentTolerancePips = ForexTolerancePips;
    
    Print("💱 إعدادات الفوركس: ATR=", currentATRMultiplier, 
          " | Min=", currentMinStopPips, " | Max=", currentMaxStopPips,
          " | التساهل=", currentTolerancePips);
}

//+------------------------------------------------------------------+
//| تطبيق إعدادات الذهب                                            |
//+------------------------------------------------------------------+
void SetGoldSettings()
{
    currentATRMultiplier = ATR_Multiplier_Gold;
    currentMinStopPips = MinStopLossPips_Gold;
    currentMaxStopPips = MaxStopLossPips_Gold;
    currentTolerancePips = GoldTolerancePips;
    
    Print("🥇 إعدادات الذهب: ATR=", currentATRMultiplier, 
          " | Min=", currentMinStopPips, " | Max=", currentMaxStopPips,
          " | التساهل=", currentTolerancePips);
}

//+------------------------------------------------------------------+
//| تطبيق إعدادات المؤشرات                                         |
//+------------------------------------------------------------------+
void SetIndexSettings()
{
    currentATRMultiplier = ATR_Multiplier_Gold * 0.5;
    currentMinStopPips = MinStopLossPips_Gold * 0.3;
    currentMaxStopPips = MaxStopLossPips_Gold * 0.5;
    currentTolerancePips = GoldTolerancePips * 0.5;
    
    Print("📊 إعدادات المؤشر: ATR=", currentATRMultiplier, 
          " | Min=", currentMinStopPips, " | Max=", currentMaxStopPips,
          " | التساهل=", currentTolerancePips);
}

//+------------------------------------------------------------------+
//| الحصول على اسم نوع الرمز                                       |
//+------------------------------------------------------------------+
string GetSymbolTypeName()
{
    switch(symbolType)
    {
        case SYMBOL_FOREX: return "فوركس 💱";
        case SYMBOL_GOLD: return "ذهب/معادن 🥇";
        case SYMBOL_INDEX: return "مؤشر 📊";
        default: return "غير معروف ❓";
    }
}

//+------------------------------------------------------------------+
//| دوال مساعدة                                                     |
//+------------------------------------------------------------------+
bool IsCooldownFinished() 
{ 
    return (TimeCurrent() - lastTradeTime) >= (CooldownMinutes * 60); 
}

double GetATRPips()
{
    double atrBuffer[];
    if (CopyBuffer(atrHandle, 0, 0, 1, atrBuffer) <= 0) return 0;
    return atrBuffer[0] / _Point;
}

void SendAlert(string msg) 
{ 
    if (EnableAlerts) Alert(msg); 
}

//+------------------------------------------------------------------+
//| حساب قوة الترند                                                 |
//+------------------------------------------------------------------+
double CalculateTrendStrength()
{
    double strength = 0.0;
    int bullishBars = 0, bearishBars = 0;
    
    int barsToAnalyze = (symbolType == SYMBOL_GOLD) ? 15 : 20;
    
    for(int i = 1; i <= barsToAnalyze; i++)
    {
        double open = iOpen(_Symbol, TimeFrame, i);
        double close = iClose(_Symbol, TimeFrame, i);
        
        if(close > open) bullishBars++;
        else if(close < open) bearishBars++;
    }
    
    strength = MathAbs(bullishBars - bearishBars) / (double)barsToAnalyze;
    return strength;
}

//+------------------------------------------------------------------+
//| حساب حجم الحركة السابقة                                         |
//+------------------------------------------------------------------+
double CalculatePreviousMove(bool isBuy)
{
    double moveSize = 0.0;
    int lookbackPeriod = (symbolType == SYMBOL_GOLD) ? 20 : 30;
    
    if(isBuy)
    {
        double highestPoint = iHigh(_Symbol, TimeFrame, 1);
        double lowestPoint = iLow(_Symbol, TimeFrame, 1);
        
        for(int i = 2; i <= lookbackPeriod; i++)
        {
            double high = iHigh(_Symbol, TimeFrame, i);
            double low = iLow(_Symbol, TimeFrame, i);
            
            if(high > highestPoint) highestPoint = high;
            if(low < lowestPoint) lowestPoint = low;
        }
        
        moveSize = highestPoint - lowestPoint;
    }
    else
    {
        double highestPoint = iHigh(_Symbol, TimeFrame, 1);
        double lowestPoint = iLow(_Symbol, TimeFrame, 1);
        
        for(int i = 2; i <= lookbackPeriod; i++)
        {
            double high = iHigh(_Symbol, TimeFrame, i);
            double low = iLow(_Symbol, TimeFrame, i);
            
            if(high > highestPoint) highestPoint = high;
            if(low < lowestPoint) lowestPoint = low;
        }
        
        moveSize = highestPoint - lowestPoint;
    }
    
    return moveSize;
}

//+------------------------------------------------------------------+
//| الستوب الذكي المحسن - حل جميع المشاكل                          |
//+------------------------------------------------------------------+
double GenerateFixedSmartStopLoss(bool isBuy, double priceEntry)
{
    if(!UseAdvancedSL || StopLossMethod != SL_SMART_SWING)
    {
        double atrValue = GetATRPips();
        double simpleStop = atrValue * (currentATRMultiplier * 0.5);
        if(simpleStop < currentMinStopPips) simpleStop = currentMinStopPips;
        if(simpleStop > currentMaxStopPips) simpleStop = currentMaxStopPips;

        return isBuy ? priceEntry - simpleStop * _Point : priceEntry + simpleStop * _Point;
    }

    if(EnableDetailedLogging)
        Print("🧠 بدء حساب الستوب الذكي المحسن...");

    // 1. البحث عن أقوى swing point مع الإصلاحات
    double extremum;
    int bestIndex = 0;
    double bestStrength = 0;

    if(isBuy)
    {
        extremum = FindFixedStrongestSwingLow(bestIndex, bestStrength);
        lastSwingLow = extremum;
    }
    else
    {
        extremum = FindFixedStrongestSwingHigh(bestIndex, bestStrength);
        lastSwingHigh = extremum;
    }

    // 2. فحص المسافة من سعر الدخول
    double distanceFromEntry = MathAbs(extremum - priceEntry) / _Point;
    if(EnableDistanceCheck && distanceFromEntry < MinDistanceFromEntry)
    {
        if(EnableDetailedLogging)
            Print("⚠️ النقطة المرجعية قريبة جداً: ", DoubleToString(distanceFromEntry, 1),
                  " < ", MinDistanceFromEntry, " - استخدام الحد الأدنى");

        // استخدام الحد الأدنى مباشرة
        double minStop = currentMinStopPips;
        return isBuy ? priceEntry - minStop * _Point : priceEntry + minStop * _Point;
    }

    // 3. حساب قوة الترند
    trendStrength = CalculateTrendStrength();

    // 4. حساب حجم الحركة السابقة
    double previousMove = CalculatePreviousMove(isBuy);

    // 5. حساب التصحيح المسموح
    correctionSize = previousMove * CorrectionAllowance;

    // 6. حساب المسافة الأساسية مع الإعدادات الديناميكية
    double atrValue = GetATRPips();
    double baseBuffer = atrValue * currentATRMultiplier;

    // 7. تطبيق مضاعف قوة الترند
    double trendMultiplier = 1.0 + (trendStrength * TrendStrengthMultiplier);

    // 8. تطبيق مضاعف قوة النقطة
    double strengthMultiplier = 1.0 + (bestStrength * 0.3);

    // 9. مضاعف خاص للرمز
    double symbolMultiplier = 1.0;
    if(symbolType == SYMBOL_GOLD)
        symbolMultiplier = 1.5;
    else if(symbolType == SYMBOL_INDEX)
        symbolMultiplier = 1.2;

    // 10. حساب المسافة النهائية
    double finalBuffer = baseBuffer * trendMultiplier * strengthMultiplier * symbolMultiplier;

    // 11. إضافة مسافة التصحيح
    finalBuffer = MathMax(finalBuffer, correctionSize / _Point);

    // 12. تطبيق الحدود الديناميكية
    if(finalBuffer < currentMinStopPips) finalBuffer = currentMinStopPips;
    if(finalBuffer > currentMaxStopPips) finalBuffer = currentMaxStopPips;

    // 13. حساب السعر النهائي
    double stopPrice = isBuy ? extremum - finalBuffer * _Point : extremum + finalBuffer * _Point;

    // 14. فحص المسافة النهائية من سعر الدخول
    double finalDistanceFromEntry = MathAbs(stopPrice - priceEntry) / _Point;
    double finalDistanceDollars = MathAbs(stopPrice - priceEntry);

    if(EnableDistanceCheck && finalDistanceFromEntry < MinDistanceFromEntry)
    {
        if(EnableDetailedLogging)
            Print("⚠️ المسافة النهائية قليلة: ", DoubleToString(finalDistanceFromEntry, 1),
                  " - تطبيق الحد الأدنى");

        double minStop = currentMinStopPips;
        stopPrice = isBuy ? priceEntry - minStop * _Point : priceEntry + minStop * _Point;
        finalDistanceFromEntry = minStop;
        finalDistanceDollars = minStop * _Point;
    }

    // طباعة التفاصيل المحسنة
    if(EnableDetailedLogging)
    {
        Print("🛡️ ═══ BOOT GOLD ستوب محسن ═══");
        Print("🥇 نوع الرمز: ", GetSymbolTypeName());
        Print("📍 ", (isBuy ? "أقوى قاع" : "أقوى قمة"), " في الشمعة ", bestIndex);
        Print("📏 المسافة من الدخول: ", DoubleToString(distanceFromEntry, 1), " نقطة");
        Print("💪 قوة النقطة: ", bestStrength, " | قوة الترند: ", DoubleToString(trendStrength, 2));
        Print("📏 ATR: ", DoubleToString(atrValue, 1), " | مضاعف ATR: ", currentATRMultiplier);
        Print("🔧 مضاعف الرمز: ", symbolMultiplier, " | التصحيح: ", DoubleToString(correctionSize/_Point, 1));
        Print("🎯 المسافة المحسوبة: ", DoubleToString(finalBuffer, 1), " نقطة");
        Print("🎯 المسافة النهائية: ", DoubleToString(finalDistanceFromEntry, 1), " نقطة");
        Print("💰 المسافة بالدولار: ", DoubleToString(finalDistanceDollars, 2), " $");
        Print("🛡️ ═══════════════════════════");
    }

    return stopPrice;
}

//+------------------------------------------------------------------+
//| البحث عن أقوى قاع - محسن                                       |
//+------------------------------------------------------------------+
double FindFixedStrongestSwingLow(int &bestIndex, double &bestStrength)
{
    double strongestLow = iLow(_Symbol, TimeFrame, MinSwingDistance); // 🔧 بدء من مسافة آمنة
    bestIndex = MinSwingDistance;
    bestStrength = 0;

    if(EnableDetailedLogging)
        Print("🔍 البحث عن أقوى قاع من الشمعة ", MinSwingDistance, " إلى ", SwingSearchRange);

    for(int i = MinSwingDistance; i <= SwingSearchRange; i++) // 🔧 بدء من مسافة آمنة
    {
        double currentLow = iLow(_Symbol, TimeFrame, i);

        // حساب قوة القاع بنطاق محسن
        int strength = 0;
        for(int j = i - StrengthAnalysisRange; j <= i + StrengthAnalysisRange; j++)
        {
            if(j >= 0 && j != i && j <= Bars(_Symbol, TimeFrame))
            {
                double compareLow = iLow(_Symbol, TimeFrame, j);
                double tolerance = currentTolerancePips * _Point; // 🔧 تساهل ديناميكي
                if(compareLow > currentLow + tolerance) strength++;
            }
        }

        // اختيار أقوى قاع مع تفضيل الأبعد
        if(currentLow < strongestLow ||
           (MathAbs(currentLow - strongestLow) <= currentTolerancePips * _Point && strength > bestStrength))
        {
            strongestLow = currentLow;
            bestIndex = i;
            bestStrength = strength;
        }
    }

    if(EnableDetailedLogging)
        Print("✅ أقوى قاع: ", DoubleToString(strongestLow, _Digits),
              " في الشمعة ", bestIndex, " بقوة ", bestStrength);

    return strongestLow;
}

//+------------------------------------------------------------------+
//| البحث عن أقوى قمة - محسن                                       |
//+------------------------------------------------------------------+
double FindFixedStrongestSwingHigh(int &bestIndex, double &bestStrength)
{
    double strongestHigh = iHigh(_Symbol, TimeFrame, MinSwingDistance); // 🔧 بدء من مسافة آمنة
    bestIndex = MinSwingDistance;
    bestStrength = 0;

    if(EnableDetailedLogging)
        Print("🔍 البحث عن أقوى قمة من الشمعة ", MinSwingDistance, " إلى ", SwingSearchRange);

    for(int i = MinSwingDistance; i <= SwingSearchRange; i++) // 🔧 بدء من مسافة آمنة
    {
        double currentHigh = iHigh(_Symbol, TimeFrame, i);

        // حساب قوة القمة بنطاق محسن
        int strength = 0;
        for(int j = i - StrengthAnalysisRange; j <= i + StrengthAnalysisRange; j++)
        {
            if(j >= 0 && j != i && j <= Bars(_Symbol, TimeFrame))
            {
                double compareHigh = iHigh(_Symbol, TimeFrame, j);
                double tolerance = currentTolerancePips * _Point; // 🔧 تساهل ديناميكي
                if(compareHigh < currentHigh - tolerance) strength++;
            }
        }

        // اختيار أقوى قمة مع تفضيل الأبعد
        if(currentHigh > strongestHigh ||
           (MathAbs(currentHigh - strongestHigh) <= currentTolerancePips * _Point && strength > bestStrength))
        {
            strongestHigh = currentHigh;
            bestIndex = i;
            bestStrength = strength;
        }
    }

    if(EnableDetailedLogging)
        Print("✅ أقوى قمة: ", DoubleToString(strongestHigh, _Digits),
              " في الشمعة ", bestIndex, " بقوة ", bestStrength);

    return strongestHigh;
}

//+------------------------------------------------------------------+
//| تحليل اتجاه السوق المحسن                                        |
//+------------------------------------------------------------------+
int AnalyzeMarketDirection()
{
    double maFastBuffer[], maSlowBuffer[], rsiBuffer[];
    if (CopyBuffer(maFastHandle, 0, 0, 1, maFastBuffer) <= 0 ||
        CopyBuffer(maSlowHandle, 0, 0, 1, maSlowBuffer) <= 0 ||
        CopyBuffer(rsiHandle, 0, 0, 1, rsiBuffer) <= 0)
        return 0;

    double maFast = maFastBuffer[0], maSlow = maSlowBuffer[0], rsiValue = rsiBuffer[0];
    double emaGap = MathAbs(maFast - maSlow) / _Point;
    if (emaGap < MinEMAGapPoints) return 0;

    double atr = GetATRPips();
    if (atr < MinATRPoints) return 0;

    if (EnableLong && IsReversalToBuy())
    {
        Print("🔍 إشارة انعكاس للشراء مؤكدة");
        return 1;
    }

    if (EnableShort && IsReversalToSell())
    {
        Print("🔍 إشارة انعكاس للبيع مؤكدة");
        return -1;
    }

    return 0;
}

//+------------------------------------------------------------------+
//| تحليل الانعكاس للشراء المحسن                                    |
//+------------------------------------------------------------------+
bool IsReversalToBuy()
{
    bool wasDownTrend = false;
    int downTrendCount = 0;
    int lookback = (symbolType == SYMBOL_GOLD) ? 8 : 12;

    for(int i = 2; i <= lookback; i++)
    {
        double high1 = iHigh(_Symbol, TimeFrame, i);
        double high2 = iHigh(_Symbol, TimeFrame, i-2);
        if(high1 > high2) downTrendCount++;
    }

    wasDownTrend = (downTrendCount >= 2);
    if(!wasDownTrend) return false;

    double recentLow = iLow(_Symbol, TimeFrame, 0);
    for(int i = 1; i <= 6; i++)
    {
        double low = iLow(_Symbol, TimeFrame, i);
        if(low < recentLow) recentLow = low;
    }

    double currentClose = iClose(_Symbol, TimeFrame, 0);
    double currentOpen = iOpen(_Symbol, TimeFrame, 0);
    double prevClose = iClose(_Symbol, TimeFrame, 1);

    bool bullishCandle = currentClose >= currentOpen;
    double tolerance = (symbolType == SYMBOL_GOLD) ? 3 * _Point : 5 * _Point;
    bool aboveRecentLow = currentClose > recentLow + tolerance;
    bool momentum = currentClose >= prevClose;

    double rsiBuffer[];
    if(CopyBuffer(rsiHandle, 0, 0, 1, rsiBuffer) <= 0) return false;
    double rsiValue = rsiBuffer[0];
    bool rsiBullish = rsiValue > 35 && rsiValue < 80;

    double maFastBuffer[], maSlowBuffer[];
    if(CopyBuffer(maFastHandle, 0, 0, 1, maFastBuffer) <= 0 ||
       CopyBuffer(maSlowHandle, 0, 0, 1, maSlowBuffer) <= 0) return false;

    double maFast = maFastBuffer[0], maSlow = maSlowBuffer[0];
    bool maRecovery = maFast >= maSlow - (15 * _Point);

    return bullishCandle && aboveRecentLow && momentum && rsiBullish && maRecovery;
}

//+------------------------------------------------------------------+
//| تحليل الانعكاس للبيع المحسن                                     |
//+------------------------------------------------------------------+
bool IsReversalToSell()
{
    bool wasUpTrend = false;
    int upTrendCount = 0;
    int lookback = (symbolType == SYMBOL_GOLD) ? 8 : 12;

    for(int i = 2; i <= lookback; i++)
    {
        double low1 = iLow(_Symbol, TimeFrame, i);
        double low2 = iLow(_Symbol, TimeFrame, i-2);
        if(low1 < low2) upTrendCount++;
    }

    wasUpTrend = (upTrendCount >= 2);
    if(!wasUpTrend) return false;

    double recentHigh = iHigh(_Symbol, TimeFrame, 0);
    for(int i = 1; i <= 6; i++)
    {
        double high = iHigh(_Symbol, TimeFrame, i);
        if(high > recentHigh) recentHigh = high;
    }

    double currentClose = iClose(_Symbol, TimeFrame, 0);
    double currentOpen = iOpen(_Symbol, TimeFrame, 0);
    double prevClose = iClose(_Symbol, TimeFrame, 1);

    bool bearishCandle = currentClose <= currentOpen;
    double tolerance = (symbolType == SYMBOL_GOLD) ? 3 * _Point : 5 * _Point;
    bool belowRecentHigh = currentClose < recentHigh - tolerance;
    bool momentum = currentClose <= prevClose;

    double rsiBuffer[];
    if(CopyBuffer(rsiHandle, 0, 0, 1, rsiBuffer) <= 0) return false;
    double rsiValue = rsiBuffer[0];
    bool rsiBearish = rsiValue < 65 && rsiValue > 20;

    double maFastBuffer[], maSlowBuffer[];
    if(CopyBuffer(maFastHandle, 0, 0, 1, maFastBuffer) <= 0 ||
       CopyBuffer(maSlowHandle, 0, 0, 1, maSlowBuffer) <= 0) return false;

    double maFast = maFastBuffer[0], maSlow = maSlowBuffer[0];
    bool maWeakness = maFast <= maSlow + (15 * _Point);

    return bearishCandle && belowRecentHigh && momentum && rsiBearish && maWeakness;
}

//+------------------------------------------------------------------+
//| فتح الصفقة مع الستوب المحسن                                     |
//+------------------------------------------------------------------+
void OpenTrade(int direction)
{
    double price = (direction == 1) ? SymbolInfoDouble(_Symbol, SYMBOL_ASK) : SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double sl = GenerateFixedSmartStopLoss(direction == 1, price);

    double slDistance = MathAbs(price - sl) / _Point;
    double slDistanceDollars = MathAbs(price - sl);

    Print("💰 سعر الدخول: ", DoubleToString(price, _Digits));
    Print("🛡️ الستوب المحسن: ", DoubleToString(sl, _Digits));
    Print("📏 مسافة الستوب: ", DoubleToString(slDistance, 1), " نقطة");
    Print("💵 مسافة الستوب: ", DoubleToString(slDistanceDollars, 2), " دولار");

    bool result = (direction == 1) ? tradeInstance.Buy(FixedLotSize, _Symbol, price, sl, 0)
                                   : tradeInstance.Sell(FixedLotSize, _Symbol, price, sl, 0);

    if (result)
    {
        positionTicket = tradeInstance.ResultOrder();
        positionOpen = true;
        lotRemaining = FixedLotSize;
        lastAdjustedSL = sl;
        lastTradeTime = TimeCurrent();
        entryPrice = price;
        positionDirection = direction;

        string directionText = (direction == 1) ? "شراء" : "بيع";

        Print("✅ ═══════════════════════════════════════");
        Print("🥇 فتح صفقة ", directionText, " BOOT GOLD 2025");
        Print("💰 السعر: ", DoubleToString(price, _Digits));
        Print("🛡️ الستوب المحسن: ", DoubleToString(sl, _Digits));
        Print("📏 المسافة: ", DoubleToString(slDistance, 1), " نقطة");
        Print("💵 المسافة: ", DoubleToString(slDistanceDollars, 2), " دولار");
        Print("🥇 نوع الرمز: ", GetSymbolTypeName());
        Print("🔧 إصلاحات مطبقة: جميع المشاكل محلولة");
        Print("✅ ═══════════════════════════════════════");

        string alertMsg = "🥇 فتح صفقة " + directionText + " BOOT GOLD - ستوب محسن " +
                         DoubleToString(slDistanceDollars, 1) + "$ (" +
                         DoubleToString(slDistance, 0) + " نقطة)";
        SendAlert(alertMsg);
    }
    else
    {
        Print("❌ فشل فتح الصفقة. الخطأ: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| انتظار وتأكيد الاتجاه                                           |
//+------------------------------------------------------------------+
void WaitAndConfirmMarketDirection()
{
    int confirmCounter = 0;
    int lastConfirmedDirection = 0;

    Print("🔍 بدء عملية التأكيد المحسن...");

    for (int i = 0; i < confirmRequired; i++)
    {
        int direction = AnalyzeMarketDirection();

        if (i == 0)
            lastConfirmedDirection = direction;
        else if (direction != lastConfirmedDirection || direction == 0)
        {
            Print("❗ التحليل غير متطابق - إلغاء");
            return;
        }

        confirmCounter++;
        string dirText = (direction == 1) ? "شراء" : (direction == -1) ? "بيع" : "لا شيء";
        Print("✅ تأكيد ", confirmCounter, "/", confirmRequired, " للاتجاه: ", dirText);

        if(i < confirmRequired - 1)
            Sleep(confirmPauseMS);
    }

    if (confirmCounter == confirmRequired && lastConfirmedDirection != 0)
    {
        string directionText = (lastConfirmedDirection == 1) ? "شراء" : "بيع";
        SendAlert("🥇 تأكيد " + directionText + " BOOT GOLD مع ستوب محسن!");
        OpenTrade(lastConfirmedDirection);
    }
}

//+------------------------------------------------------------------+
//| إدارة الصفقة المفتوحة                                           |
//+------------------------------------------------------------------+
void ManageOpenPosition()
{
    if (!positionOpen) return;
    if (!PositionSelect(_Symbol))
    {
        positionOpen = false;
        lotRemaining = 0.0;
        positionTicket = 0;
        positionDirection = 0;
        return;
    }

    double currentPrice = (positionDirection == 1) ?
        SymbolInfoDouble(_Symbol, SYMBOL_BID) : SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    double tp1Price = (positionDirection == 1) ?
        entryPrice + TargetPips_TP1 * _Point : entryPrice - TargetPips_TP1 * _Point;
    double tp2Price = (positionDirection == 1) ?
        entryPrice + TargetPips_TP2 * _Point : entryPrice - TargetPips_TP2 * _Point;
    double tp3Price = (positionDirection == 1) ?
        entryPrice + TargetPips_TP3 * _Point : entryPrice - TargetPips_TP3 * _Point;

    ulong ticket = PositionGetInteger(POSITION_TICKET);
    double posLots = PositionGetDouble(POSITION_VOLUME);
    double slCurrent = PositionGetDouble(POSITION_SL);

    // إدارة الأهداف
    if (((positionDirection == 1) && currentPrice >= tp1Price) ||
        ((positionDirection == -1) && currentPrice <= tp1Price))
    {
        double lotToClose = posLots * LotPercent_TP1;
        if (lotToClose > 0 && lotRemaining > 0)
        {
            if (tradeInstance.PositionClosePartial(ticket, lotToClose))
            {
                lotRemaining -= lotToClose;
                Print("🎯 تحقيق TP1 BOOT GOLD - إغلاق: ", DoubleToString(lotToClose, 2), " لوت");
                SendAlert("✅ TP1 BOOT GOLD محقق!");
            }
        }

        double newSL = (positionDirection == 1) ?
            entryPrice + SL_OffsetPips_AfterTP1 * _Point :
            entryPrice - SL_OffsetPips_AfterTP1 * _Point;

        if ((positionDirection == 1 && newSL > slCurrent) ||
            (positionDirection == -1 && newSL < slCurrent))
        {
            if (tradeInstance.PositionModify(ticket, newSL, 0))
            {
                lastAdjustedSL = newSL;
                Print("✅ تأمين BOOT GOLD - نقل الستوب للدخول");
                SendAlert("🛡️ تأمين صفقة BOOT GOLD");
            }
        }
    }

    if (((positionDirection == 1) && currentPrice >= tp2Price) ||
        ((positionDirection == -1) && currentPrice <= tp2Price))
    {
        double lotToClose = posLots * LotPercent_TP2;
        if (lotToClose > 0 && lotRemaining > 0)
        {
            if (tradeInstance.PositionClosePartial(ticket, lotToClose))
            {
                lotRemaining -= lotToClose;
                SendAlert("✅ TP2 BOOT GOLD محقق!");
            }
        }
    }

    if (((positionDirection == 1) && currentPrice >= tp3Price) ||
        ((positionDirection == -1) && currentPrice <= tp3Price))
    {
        if (tradeInstance.PositionClosePartial(ticket, lotRemaining))
        {
            positionOpen = false;
            SendAlert("🥇 TP3 BOOT GOLD - إنجاز كامل!");
        }
    }
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    if (ManualClose && PositionSelect(_Symbol))
    {
        tradeInstance.PositionClose(_Symbol);
        positionOpen = false;
        lotRemaining = 0;
        lastTradeTime = TimeCurrent();
        SendAlert("🔧 إغلاق يدوي BOOT GOLD");
        return;
    }

    if (positionOpen)
    {
        ManageOpenPosition();
    }
    else if (IsCooldownFinished())
    {
        WaitAndConfirmMarketDirection();
    }
}
