#include <Trade\Trade.mqh>

CTrade tradeInstance;

// === إعدادات الإدخال ===
input double RiskPercent          = 2.0;
input int    TargetsCount         = 3;
input double FixedLotSize         = 0.1;
input ENUM_TIMEFRAMES TimeFrame   = PERIOD_M15;
input bool   EnableLong           = true;
input bool   EnableShort          = true;
input int    CooldownMinutes      = 30;
input double BreakEvenBufferPips = 5.0;  // نقاط نقل الستوب فوق الدخول

input bool   EnableMACDFilter     = true; // تفعيل/تعطيل فلتر MACD

// معاملات ATR لتحديد SL وTP
input double ATR_Multiplier_SL = 1.5;
input double ATR_Multiplier_TP = 3.0;

// معاملات MACD
input int MACD_FastEMA = 12;
input int MACD_SlowEMA = 26;
input int MACD_SignalSMA = 9;

// === متغيرات داخلية ===
ulong positionTicket = 0;
double lotRemaining = 0.0;
bool positionOpen = false;
datetime lastTradeTime = 0;
double lotStep = 0.0;
bool isAnalysisDone = false;
int analysisDirection = 0; // 1 = شراء, -1 = بيع

// ==================== دالة لحساب مستويات Pivot اليومية ====================
struct PivotLevels {
  double PP;
  double R1;
  double S1;
  double R2;
  double S2;
  double R3;
  double S3;
};

bool CalculateDailyPivotLevels(PivotLevels &pl)
{
    MqlRates rates[];

    if(CopyRates(_Symbol, PERIOD_D1, 1, 1, rates) <= 0)
    {
        Print("❌ فشل في جلب بيانات اليوم السابق لحساب Pivot");
        return false;
    }

    double high = rates[0].high;
    double low = rates[0].low;
    double close = rates[0].close;

    pl.PP = (high + low + close) / 3.0;
    pl.R1 = 2 * pl.PP - low;
    pl.S1 = 2 * pl.PP - high;
    pl.R2 = pl.PP + (high - low);
    pl.S2 = pl.PP - (high - low);
    pl.R3 = high + 2 * (pl.PP - low);
    pl.S3 = low - 2 * (high - pl.PP);

    return true;
}

double AdjustStopLossWithPivot(double sl, double entryPrice, int positionType, PivotLevels &pl)
{
    if(positionType == POSITION_TYPE_BUY)
    {
        if(sl < pl.S1 && pl.S1 < entryPrice)
            sl = pl.S1;
        else if(sl < pl.S2 && pl.S2 < entryPrice)
            sl = pl.S2;
        else if(sl < pl.S3 && pl.S3 < entryPrice)
            sl = pl.S3;
    }
    else if(positionType == POSITION_TYPE_SELL)
    {
        if(sl > pl.R1 && pl.R1 > entryPrice)
            sl = pl.R1;
        else if(sl > pl.R2 && pl.R2 > entryPrice)
            sl = pl.R2;
        else if(sl > pl.R3 && pl.R3 > entryPrice)
            sl = pl.R3;
    }
    return sl;
}

// تبسيط شرط MACD: فقط الاتجاه (MACD فوق أو تحت الإشارة)
bool IsMACDSignalValid(int direction)
{
    if(!EnableMACDFilter)
    {
        Print("ℹ️ فلتر MACD معطل - السماح بالدخول بدون شرط MACD");
        return true;
    }

    double macdBuffer[], signalBuffer[];
    int macdHandle = iMACD(_Symbol, TimeFrame, MACD_FastEMA, MACD_SlowEMA, MACD_SignalSMA, PRICE_CLOSE);

    if(macdHandle == INVALID_HANDLE)
    {
        Print("❌ خطأ في إنشاء مؤشر MACD.");
        return false;
    }

    if(CopyBuffer(macdHandle, 0, 0, 1, macdBuffer) <= 0 ||
       CopyBuffer(macdHandle, 1, 0, 1, signalBuffer) <= 0)
    {
        Print("❌ خطأ في نسخ بيانات MACD.");
        IndicatorRelease(macdHandle);
        return false;
    }

    IndicatorRelease(macdHandle);

    double macdCurrent = macdBuffer[0];
    double signalCurrent = signalBuffer[0];

    if(direction == 1)
    {
        bool valid = (macdCurrent > signalCurrent);
        PrintFormat("🔍 MACD شراء: MACD=%.5f > Signal=%.5f ؟ %s", macdCurrent, signalCurrent, valid ? "نعم" : "لا");
        return valid;
    }
    else if(direction == -1)
    {
        bool valid = (macdCurrent < signalCurrent);
        PrintFormat("🔍 MACD بيع: MACD=%.5f < Signal=%.5f ؟ %s", macdCurrent, signalCurrent, valid ? "نعم" : "لا");
        return valid;
    }

    return false;
}

int OnInit()
{
    lotStep = FixedLotSize / TargetsCount;
    return INIT_SUCCEEDED;
}

bool IsCooldownFinished()
{
    return (TimeCurrent() - lastTradeTime) >= (CooldownMinutes * 60);
}

void StartMarketAnalysis()
{
    Print("🔎 بدء تحليل السوق...");
    double maFastBuffer[], maSlowBuffer[];

    int maFastHandle = iMA(_Symbol, TimeFrame, 10, 0, MODE_EMA, PRICE_CLOSE);
    int maSlowHandle = iMA(_Symbol, TimeFrame, 20, 0, MODE_EMA, PRICE_CLOSE);

    if (maFastHandle == INVALID_HANDLE || maSlowHandle == INVALID_HANDLE)
    {
        Print("❌ خطأ في إنشاء مؤشرات الموفينج أفراز.");
        return;
    }

    if (CopyBuffer(maFastHandle, 0, 0, 1, maFastBuffer) <= 0 ||
        CopyBuffer(maSlowHandle, 0, 0, 1, maSlowBuffer) <= 0)
    {
        Print("❌ خطأ في نسخ بيانات مؤشرات الموفينج أفراز.");
        IndicatorRelease(maFastHandle);
        IndicatorRelease(maSlowHandle);
        return;
    }

    double maFast = maFastBuffer[0];
    double maSlow = maSlowBuffer[0];

    PrintFormat("📊 موفينج أفراز سريع = %.5f، بطيء = %.5f", maFast, maSlow);

    if (maFast > maSlow && EnableLong)
    {
        analysisDirection = 1;
        Print("✅ إشارة شراء.");
    }
    else if (maFast < maSlow && EnableShort)
    {
        analysisDirection = -1;
        Print("✅ إشارة بيع.");
    }
    else
    {
        analysisDirection = 0;
        Print("⚠️ لا توجد إشارة واضحة (محايد).");
    }

    isAnalysisDone = true;

    IndicatorRelease(maFastHandle);
    IndicatorRelease(maSlowHandle);
}

void OpenTrade()
{
    Print("⏳ محاولة فتح صفقة...");

    if (PositionsTotal() >= 1)
    {
        Print("🚫 هناك صفقة مفتوحة بالفعل، لن يتم فتح صفقة جديدة.");
        return;
    }

    if (!IsCooldownFinished())
    {
        Print("⌛ وقت الانتظار لم ينتهِ بعد.");
        return;
    }

    if (!isAnalysisDone)
    {
        Print("⚠️ لم يتم إجراء تحليل السوق بعد.");
        return;
    }

    if(analysisDirection == 0)
    {
        Print("⚠️ لا توجد إشارة فتح صفقة.");
        return;
    }

    if(!IsMACDSignalValid(analysisDirection))
    {
        Print("⚠️ شرط MACD غير مستوفى، لا يتم فتح الصفقة.");
        return;
    }

    double price, sl, tp;

    double atr = iATR(_Symbol, TimeFrame, 14);
    double pointSize = SymbolInfoDouble(_Symbol, SYMBOL_POINT);

    double stopLossPips = atr * ATR_Multiplier_SL / pointSize;
    double targetPips = atr * ATR_Multiplier_TP / pointSize;

    PivotLevels pl;
    if(!CalculateDailyPivotLevels(pl))
    {
        Print("❌ تعذر حساب مستويات Pivot، فتح الصفقة بدون تعديل الدعم/المقاومة");
    }

    if (analysisDirection == 1) // شراء
    {
        price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
        sl = price - stopLossPips * pointSize;
        sl = AdjustStopLossWithPivot(sl, price, POSITION_TYPE_BUY, pl);
        tp = price + targetPips * pointSize;
        if (tradeInstance.PositionOpen(_Symbol, ORDER_TYPE_BUY, FixedLotSize, price, sl, tp))
        {
            lastTradeTime = TimeCurrent();
            isAnalysisDone = false;
            PrintFormat("✅ تم فتح صفقة شراء بسعر %.5f, SL=%.5f, TP=%.5f", price, sl, tp);
        }
        else
        {
            Print("❌ فشل في فتح صفقة شراء.");
        }
    }
    else if (analysisDirection == -1) // بيع
    {
        price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
        sl = price + stopLossPips * pointSize;
        sl = AdjustStopLossWithPivot(sl, price, POSITION_TYPE_SELL, pl);
        tp = price - targetPips * pointSize;
        if (tradeInstance.PositionOpen(_Symbol, ORDER_TYPE_SELL, FixedLotSize, price, sl, tp))
        {
            lastTradeTime = TimeCurrent();
            isAnalysisDone = false;
            PrintFormat("✅ تم فتح صفقة بيع بسعر %.5f, SL=%.5f, TP=%.5f", price, sl, tp);
        }
        else
        {
            Print("❌ فشل في فتح صفقة بيع.");
        }
    }
}

void ManageTrade()
{
    if(!PositionSelect(_Symbol))
    {
        if (positionOpen)
        {
            lastTradeTime = TimeCurrent();
            Print("🚨 تم إغلاق الصفقة - بدء وقت الانتظار Cooldown");
        }
        positionOpen = false;
        lotRemaining = 0.0;
        positionTicket = 0;
        return;
    }

    positionOpen = true;
    positionTicket = PositionGetInteger(POSITION_TICKET);
    lotRemaining = PositionGetDouble(POSITION_VOLUME);

    double entryPrice = PositionGetDouble(POSITION_PRICE_OPEN);
    long type = PositionGetInteger(POSITION_TYPE);
    double currentPrice = (type == POSITION_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_BID) : SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double pointSize = SymbolInfoDouble(_Symbol, SYMBOL_POINT);

    double atr = iATR(_Symbol, TimeFrame, 14);
    double targetPips = atr * ATR_Multiplier_TP / pointSize;

    double target1 = entryPrice + ((type == POSITION_TYPE_BUY) ? 50 * pointSize : -50 * pointSize);
    double target2 = entryPrice + ((type == POSITION_TYPE_BUY) ? 2 * targetPips * pointSize : -2 * targetPips * pointSize);
    double target3 = entryPrice + ((type == POSITION_TYPE_BUY) ? 3 * targetPips * pointSize : -3 * targetPips * pointSize);

    double sl = PositionGetDouble(POSITION_SL);

    double breakEvenLevel = entryPrice + ((type == POSITION_TYPE_BUY) ? (BreakEvenBufferPips * pointSize) : -(BreakEvenBufferPips * pointSize));
    double halfTarget1 = entryPrice + ((type == POSITION_TYPE_BUY) ? (targetPips * 0.5 * pointSize) : -(targetPips * 0.5 * pointSize));
    bool isSLMoved = (type == POSITION_TYPE_BUY && sl >= breakEvenLevel) || (type == POSITION_TYPE_SELL && sl <= breakEvenLevel);

    if (!isSLMoved && (
            (type == POSITION_TYPE_BUY && currentPrice >= halfTarget1) ||
            (type == POSITION_TYPE_SELL && currentPrice <= halfTarget1)))
    {
        bool modified = tradeInstance.PositionModify(_Symbol, breakEvenLevel, PositionGetDouble(POSITION_TP));
        if(modified)
            PrintFormat("🛡️ تم نقل الستوب إلى نقطة الدخول +%.1f نقطة", BreakEvenBufferPips);
        else
            Print("❌ فشل في نقل الستوب إلى Breakeven + Buffer");
    }

    if (lotRemaining > lotStep && ((type == POSITION_TYPE_BUY && currentPrice >= target1) || (type == POSITION_TYPE_SELL && currentPrice <= target1)))
    {
        if(tradeInstance.PositionClosePartial(positionTicket, lotStep))
        {
            lotRemaining -= lotStep;
            double newSL = entryPrice;
            if ((type == POSITION_TYPE_BUY && sl < newSL) || (type == POSITION_TYPE_SELL && sl > newSL))
                tradeInstance.PositionModify(_Symbol, newSL, PositionGetDouble(POSITION_TP));
        }
    }

    if (lotRemaining > lotStep && ((type == POSITION_TYPE_BUY && currentPrice >= target2) || (type == POSITION_TYPE_SELL && currentPrice <= target2)))
    {
        if(tradeInstance.PositionClosePartial(positionTicket, lotStep))
        {
            lotRemaining -= lotStep;
            double newSL = target1;
            if ((type == POSITION_TYPE_BUY && sl < newSL) || (type == POSITION_TYPE_SELL && sl > newSL))
                tradeInstance.PositionModify(_Symbol, newSL, PositionGetDouble(POSITION_TP));
        }
    }

    if (lotRemaining > 0 && ((type == POSITION_TYPE_BUY && currentPrice >= target3) || (type == POSITION_TYPE_SELL && currentPrice <= target3)))
    {
        if(tradeInstance.PositionClosePartial(positionTicket, lotRemaining))
        {
            lotRemaining = 0;
            positionOpen = false;
            lastTradeTime = TimeCurrent();
            Print("🎯 الصفقة وصلت للهدف الأخير - بدء Cooldown");
        }
    }
}

void OnTick()
{
    ManageTrade();

    if (!positionOpen && IsCooldownFinished() && !isAnalysisDone)
        StartMarketAnalysis();

    if (!positionOpen && IsCooldownFinished() && isAnalysisDone)
        OpenTrade();
}
