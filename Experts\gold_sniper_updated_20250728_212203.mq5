//+------------------------------------------------------------------+
//|           الخال سنايبر | Gold Sniper EA (Pro Logic v7)          |
//+------------------------------------------------------------------+
#property copyright "By Ha Trader"
#property version   "7.0"
#property strict

#include <Trade\Trade.mqh>
CTrade trade;

// ======= إعدادات المستخدم =======
input string    mySymbol          = "XAUUSD";
input double    lot1              = 0.01; // دخول أساسي
input double    lot2              = 0.02; // تعزيز أول
input double    lot3              = 0.03; // تعزيز ثاني
input double    lot4              = 0.04; // تعزيز ثالث
input int       levelsAboveBelow  = 15;
input double    levelDistance     = 5.0;
input double    mainLevel1        = 2.40;
input double    mainLevel2        = 7.40;
input bool      EnableBuy         = true;
input bool      EnableSell        = true;
input bool      EnablePending     = true;
input bool      EnableMarket      = true;

// ======= متغيرات الدورة =======
double entry_prices[4];
int entry_count = 0;
double first_entry = 0.0;
int tradeDirection = 0; // 0 = لا يوجد، 1 = شراء، -1 = بيع
bool inCycle = false;
datetime dayStartTime = 0;
bool drawnLevelsToday = false;

// ======= متغيرات دورة التعويض العكسية =======
double reverse_entry_prices[4];
int reverse_entry_count = 0;
double reverse_first_entry = 0.0;
int reverseDirection = 0; // -1 = بيع عكسي, 1 = شراء عكسي
bool inReverseCycle = false;

// ======= نوع الدورة =======
enum CycleType { NATURAL, REVERSE };
CycleType currentCycle = NATURAL;

//+------------------------------------------------------------------+
//| دالة حساب السبريد بالدقة                                         |
//+------------------------------------------------------------------+
double GetSpread()
{
    double ask = SymbolInfoDouble(mySymbol, SYMBOL_ASK);
    double bid = SymbolInfoDouble(mySymbol, SYMBOL_BID);
    return NormalizeDouble(ask - bid, _Digits);
}

//+------------------------------------------------------------------+
//| رسم المستويات على الشارت (كل يوم جديد)                          |
//+------------------------------------------------------------------+
void DrawLevels()
{
    double price = SymbolInfoDouble(mySymbol, SYMBOL_BID);
    double basePrice = NormalizeDouble(price, 2);

    int total = ObjectsTotal(0, 0);
    for(int i=total-1; i>=0; i--)
    {
        string name = ObjectName(0, i, 0);
        if(StringFind(name, "SniperLevel") == 0)
            ObjectDelete(0, name);
    }

    for(int d=-levelsAboveBelow; d<=levelsAboveBelow; d++)
    {
        double lvls[2] = {mainLevel1, mainLevel2};
        for(int k=0; k<2; k++)
        {
            double lvl = MathFloor(basePrice/10)*10 + lvls[k] + d*levelDistance;
            string name = "SniperLevel_" + DoubleToString(lvl,2);
            if(ObjectCreate(0, name, OBJ_HLINE, 0, 0, lvl))
            {
                ObjectSetInteger(0, name, OBJPROP_COLOR, clrOrange);
                ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
            }
        }
    }
    drawnLevelsToday = true;
}

//+------------------------------------------------------------------+
//| التحقق من أن السعر هو مستوى رئيسي (2.40 أو 7.40)                |
//+------------------------------------------------------------------+
bool IsMainLevel(double price)
{
    double x = NormalizeDouble(price, 2);
    double dec = x - MathFloor(x/10)*10;
    return (MathAbs(dec - mainLevel1) < 0.01 || MathAbs(dec - mainLevel2) < 0.01);
}

//+------------------------------------------------------------------+
//| التحقق من إمكانية الدخول على مستوى رئيسي                        |
//+------------------------------------------------------------------+
bool IsEligibleForEntry(int direction)
{
    double level = 0.0;
    double spread = GetSpread();
    if(direction == 1)
        level = NormalizeDouble(SymbolInfoDouble(mySymbol, SYMBOL_ASK) - spread, 2);
    else if(direction == -1)
        level = NormalizeDouble(SymbolInfoDouble(mySymbol, SYMBOL_BID) + spread, 2);
    else
        return false;

    return (IsMainLevel(level) && IsExpectedLevel(level) && !IsOrderExist(level, direction) && !AlreadyEnhanced(level));
}

//+------------------------------------------------------------------+
//| التحقق إذا في صفقة على نفس المستوى                              |
//+------------------------------------------------------------------+
bool IsOrderExist(double level, int dir)
{
    for(int i=0; i<PositionsTotal(); i++)
    {
        ulong ticket = PositionGetTicket(i);
        if(PositionGetString(POSITION_SYMBOL) == mySymbol &&
           PositionGetInteger(POSITION_TYPE) == (dir==1?POSITION_TYPE_BUY:POSITION_TYPE_SELL))
        {
            double price = PositionGetDouble(POSITION_PRICE_OPEN);
            if(MathAbs(price-level) < 0.01)
                return true;
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| التحقق إذا السعر مساوي بالضبط لمستوى تعزيز مطلوب                |
//+------------------------------------------------------------------+
bool IsExpectedLevel(double price)
{
    if(currentCycle == NATURAL)
    {
        if(entry_count == 0)
            return true;
        double expected = first_entry - tradeDirection * levelDistance * entry_count;
        return (NormalizeDouble(price,2) == NormalizeDouble(expected,2));
    }
    else
    {
        if(reverse_entry_count == 0)
            return true;
        double expected = reverse_first_entry - reverseDirection * levelDistance * reverse_entry_count;
        return (NormalizeDouble(price,2) == NormalizeDouble(expected,2));
    }
}

//+------------------------------------------------------------------+
//| التحقق هل المستوى تم الدخول عنده بالفعل                          |
//+------------------------------------------------------------------+
bool AlreadyEnhanced(double level)
{
    if(currentCycle == NATURAL)
    {
        for(int i=0; i<entry_count; i++)
            if(MathAbs(entry_prices[i] - level) < 0.01)
                return true;
    }
    else
    {
        for(int i=0; i<reverse_entry_count; i++)
            if(MathAbs(reverse_entry_prices[i] - level) < 0.01)
                return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| تحديد الاتجاه عند أول لمس لمستوى جديد                            |
//+------------------------------------------------------------------+
void DetectDirection()
{
    double price = NormalizeDouble(SymbolInfoDouble(mySymbol, SYMBOL_BID),2);

    if(tradeDirection == 0 && entry_count == 0 && reverse_entry_count == 0)
    {
        if(IsMainLevel(price))
        {
            if(EnableBuy)
                tradeDirection = 1;
            else if(EnableSell)
                tradeDirection = -1;
            first_entry = price;
            entry_count = 0;
            inCycle = true;
            currentCycle = NATURAL;
            reverse_entry_count = 0;
            inReverseCycle = false;
        }
    }
}
//+------------------------------------------------------------------+
//| إدارة الصفقات والدخول/التعزيز + منطق 4 مناطق                    |
//+------------------------------------------------------------------+
void ManageCycle()
{
    if(currentCycle == NATURAL)
        ManageNaturalCycle();
    else
        ManageReverseCycle();
}

//+------------------------------------------------------------------+
//| إدارة دورة طبيعية (دخول + 3 تعزيزات)                            |
//+------------------------------------------------------------------+
void ManageNaturalCycle()
{
    if(!inCycle || entry_count >= 4)
        return;

    if(IsEligibleForEntry(tradeDirection))
    {
        double spread = GetSpread();
        double level = (tradeDirection == 1) ? NormalizeDouble(SymbolInfoDouble(mySymbol, SYMBOL_ASK) - spread,2)
                                             : NormalizeDouble(SymbolInfoDouble(mySymbol, SYMBOL_BID) + spread,2);

        double lot = lot1;
        if(entry_count == 1) lot = lot2;
        else if(entry_count == 2) lot = lot3;
        else if(entry_count == 3) lot = lot4;

        if(EnableMarket)
        {
            if(tradeDirection==1)
                trade.Buy(lot, mySymbol);
            else
                trade.Sell(lot, mySymbol);
        }
        entry_prices[entry_count] = level;
        entry_count++;
    }

    // ----------- منطق الهدف الديناميكي بعد كل تعزيز (لغاية التعزيز الثالث فقط) -----------
    if(entry_count > 0 && entry_count < 4)
    {
        double lastEntry = entry_prices[entry_count-1];
        double targetLevel = 0.0;
        if(tradeDirection == 1)
        {
            double lvl = lastEntry + levelDistance;
            while(!IsMainLevel(lvl) && lvl < lastEntry + 10*levelDistance)
                lvl += levelDistance;
            targetLevel = lvl;
        }
        else if(tradeDirection == -1)
        {
            double lvl = lastEntry - levelDistance;
            while(!IsMainLevel(lvl) && lvl > lastEntry - 10*levelDistance)
                lvl -= levelDistance;
            targetLevel = lvl;
        }

        double current = (tradeDirection == 1) ? SymbolInfoDouble(mySymbol, SYMBOL_BID)
                                               : SymbolInfoDouble(mySymbol, SYMBOL_ASK);

        bool shouldClose = false;
        if(tradeDirection == 1 && current >= targetLevel)
            shouldClose = true;
        if(tradeDirection == -1 && current <= targetLevel)
            shouldClose = true;

        if(shouldClose)
        {
            CloseAllPositions(tradeDirection);
            entry_count = 0;
            inCycle = false;
            int prevDirection = tradeDirection;
            tradeDirection = 0;

            double lot = lot1;
            bool entered = false;
            if(EnableMarket)
            {
                if(prevDirection == 1)
                    entered = trade.Buy(lot, mySymbol);
                else if(prevDirection == -1)
                    entered = trade.Sell(lot, mySymbol);
            }
            if(!entered && EnablePending)
            {
                if(prevDirection == 1)
                    trade.BuyLimit(lot, targetLevel, mySymbol);
                else if(prevDirection == -1)
                    trade.SellLimit(lot, targetLevel, mySymbol);
            }

            tradeDirection = prevDirection;
            inCycle = true;
            first_entry = targetLevel;
            entry_count = 0;
            entry_prices[entry_count] = targetLevel;
            entry_count++;
        }
    }

    // ----------- منطق الإغلاق عند منطقة التعزيز الثالث وفتح صفقة عكسية -----------
    if(entry_count == 4)
    {
        double fourthLevel = entry_prices[3];
        double currentPrice = (tradeDirection==1) ? SymbolInfoDouble(mySymbol, SYMBOL_BID)
                                                  : SymbolInfoDouble(mySymbol, SYMBOL_ASK);

        if(MathAbs(currentPrice - fourthLevel) < 0.01)
        {
            CloseAllPositions(tradeDirection);

            // فتح صفقة عكسية فورية بلوت دخول جديد (lot1)
            int reverseDir = -tradeDirection;
            double lotReverse = lot1;
            if(reverseDir == 1)
                trade.Buy(lotReverse, mySymbol);
            else
                trade.Sell(lotReverse, mySymbol);

            // إعادة ضبط المتغيرات
            inCycle = false;
            entry_count = 1;
            tradeDirection = reverseDir;
            first_entry = fourthLevel;
            entry_prices[0] = fourthLevel;
            entry_prices[1] = 0.0;
            entry_prices[2] = 0.0;
            entry_prices[3] = 0.0;
            inCycle = true;
            return;
        }
    }
}

//+------------------------------------------------------------------+
//| إدارة دورة عكسية (دخول عكسي + تعزيزات)                          |
//+------------------------------------------------------------------+
void ManageReverseCycle()
{
    if(!inReverseCycle || reverse_entry_count >= 4)
        return;

    int direction = reverseDirection;
    double spread = GetSpread();

    double level = (direction == 1) ? NormalizeDouble(SymbolInfoDouble(mySymbol, SYMBOL_ASK) - spread,2)
                                    : NormalizeDouble(SymbolInfoDouble(mySymbol, SYMBOL_BID) + spread,2);

    double lot = lot1;
    if(reverse_entry_count == 1) lot = lot2;
    else if(reverse_entry_count == 2) lot = lot3;
    else if(reverse_entry_count == 3) lot = lot4;

    bool eligible = IsMainLevel(level) && IsExpectedLevel(level) && !IsOrderExist(level, direction) && !AlreadyEnhanced(level);

    if(eligible)
    {
        if(direction==1)
            trade.Buy(lot, mySymbol);
        else
            trade.Sell(lot, mySymbol);

        reverse_entry_prices[reverse_entry_count] = level;
        reverse_entry_count++;
    }

    // ----------- منطق هدف التعويض بعد كل تعزيز (لغاية الثالث فقط) -----------
    if(reverse_entry_count > 0 && reverse_entry_count < 4)
    {
        double lastEntry = reverse_entry_prices[reverse_entry_count-1];
        double targetLevel = 0.0;
        if(direction == 1)
        {
            double lvl = lastEntry + levelDistance;
            while(!IsMainLevel(lvl) && lvl < lastEntry + 10*levelDistance)
                lvl += levelDistance;
            targetLevel = lvl;
        }
        else if(direction == -1)
        {
            double lvl = lastEntry - levelDistance;
            while(!IsMainLevel(lvl) && lvl > lastEntry - 10*levelDistance)
                lvl -= levelDistance;
            targetLevel = lvl;
        }

        double current = (direction == 1) ? SymbolInfoDouble(mySymbol, SYMBOL_BID)
                                          : SymbolInfoDouble(mySymbol, SYMBOL_ASK);

        bool shouldClose = false;
        if(direction == 1 && current >= targetLevel)
            shouldClose = true;
        if(direction == -1 && current <= targetLevel)
            shouldClose = true;

        if(shouldClose)
        {
            CloseAllPositions(direction);
            reverse_entry_count = 0;
            inReverseCycle = false;
            int prevDirection = direction;
            reverseDirection = 0;

            double lot = lot1;
            bool entered = false;
            if(EnableMarket)
            {
                if(prevDirection == 1)
                    entered = trade.Buy(lot, mySymbol);
                else if(prevDirection == -1)
                    entered = trade.Sell(lot, mySymbol);
            }
            if(!entered && EnablePending)
            {
                if(prevDirection == 1)
                    trade.BuyLimit(lot, targetLevel, mySymbol);
                else if(prevDirection == -1)
                    trade.SellLimit(lot, targetLevel, mySymbol);
            }

            reverseDirection = prevDirection;
            inReverseCycle = true;
            reverse_first_entry = targetLevel;
            reverse_entry_count = 0;
            reverse_entry_prices[reverse_entry_count] = targetLevel;
            reverse_entry_count++;
        }
    }

    // ----------- منطق الإغلاق عند منطقة التعزيز الثالث وفتح صفقة عكسية جديدة -----------
    if(reverse_entry_count == 4)
    {
        double fourthLevel = reverse_entry_prices[3];
        double currentPrice = (direction==1) ? SymbolInfoDouble(mySymbol, SYMBOL_BID)
                                             : SymbolInfoDouble(mySymbol, SYMBOL_ASK);

        if(MathAbs(currentPrice - fourthLevel) < 0.01)
        {
            CloseAllPositions(direction);

            // فتح صفقة عكسية عكسية فورية (عودة للاتجاه الأصلي)
            int newDir = -direction;
            double lotNew = lot1;
            if(newDir == 1)
                trade.Buy(lotNew, mySymbol);
            else
                trade.Sell(lotNew, mySymbol);

            inReverseCycle = false;
            reverse_entry_count = 0;
            reverseDirection = 0;
            currentCycle = NATURAL;
            inCycle = true;
            entry_count = 1;
            tradeDirection = newDir;
            first_entry = fourthLevel;
            entry_prices[0] = fourthLevel;
            entry_prices[1] = 0.0;
            entry_prices[2] = 0.0;
            entry_prices[3] = 0.0;
            return;
        }
    }
}

//+------------------------------------------------------------------+
//| دالة إغلاق الصفقات للاتجاه الحالي                              |
//+------------------------------------------------------------------+
void CloseAllPositions(int dir)
{
    for(int i=PositionsTotal()-1; i>=0; i--)
    {
        if(PositionGetSymbol(i)==mySymbol)
        {
            long posType = PositionGetInteger(POSITION_TYPE);
            if((dir==1 && posType==POSITION_TYPE_BUY) ||
               (dir==-1 && posType==POSITION_TYPE_SELL))
            {
                trade.PositionClose(PositionGetTicket(i));
            }
        }
    }
}

//+------------------------------------------------------------------+
//| الدوال الأساسية الأخرى (OnInit و OnTick)                         |
//+------------------------------------------------------------------+
int OnInit()
{
    dayStartTime = iTime(mySymbol, PERIOD_D1, 0);
    DrawLevels();
    Print("الخال سنايبر يعمل الإصدار 7.0 - منطق تعزيز 4 مناطق واغلاق صارم");
    return(INIT_SUCCEEDED);
}

void OnTick()
{
    datetime today = iTime(mySymbol, PERIOD_D1, 0);
    if(today != dayStartTime)
    {
        dayStartTime = today;
        drawnLevelsToday = false;
    }
    if(!drawnLevelsToday)
        DrawLevels();

    DetectDirection();
    if(currentCycle==NATURAL && tradeDirection!=0)
        ManageCycle();
    if(currentCycle==REVERSE && reverseDirection!=0)
        ManageCycle();
}