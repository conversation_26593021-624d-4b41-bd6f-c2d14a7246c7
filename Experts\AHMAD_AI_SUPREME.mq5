//+------------------------------------------------------------------+
//|     Expert Advisor - AHMAD AI SUPREME (Ultimate Intelligence)   |
//|    🤖 شبكة عصبية + تعلم آلي + تحليل أنماط متقدم + فيبوناتشي    |
//| 🧠 ذكاء اصطناعي متقدم + Elliott Wave + تعلم من الأخطاء + تكيف |
//|        ✨ أعلى مستوى ذكاء اصطناعي في MetaTrader ✨           |
//+------------------------------------------------------------------+
#property strict
#include <Trade\Trade.mqh>
CTrade tradeInstance;

// ⚙️ الإعدادات الأساسية
input double RiskPercent = 2.0;
input double TargetPips_TP1 = 150;
input double TargetPips_TP2 = 300;
input double TargetPips_TP3 = 500;
input double SL_OffsetPips_AfterTP1 = 20;
input double FixedLotSize = 0.1;
input double LotPercent_TP1 = 0.3;
input double LotPercent_TP2 = 0.5;
input double LotPercent_TP3 = 0.5;
input ENUM_TIMEFRAMES TimeFrame = PERIOD_M15;
input bool EnableLong = true;
input bool EnableShort = true;
input int CooldownMinutes = 30;
input bool ManualClose = false;

// 🤖 إعدادات الذكاء الاصطناعي المتقدم
input int AI_LearningPeriod = 100;           // فترة التعلم (عدد الصفقات)
input double AI_AdaptationRate = 0.1;        // معدل التكيف (0.01-0.5)
input bool EnableNeuralNetwork = true;       // تفعيل الشبكة العصبية
input bool EnablePatternRecognition = true;  // تفعيل كشف الأنماط
input bool EnableElliottWave = true;         // تفعيل تحليل Elliott Wave
input bool EnableFibonacci = true;           // تفعيل مستويات فيبوناتشي
input bool EnableSelfOptimization = true;    // تفعيل التحسين الذاتي
input double AI_ConfidenceThreshold = 0.75;  // حد الثقة للذكاء الاصطناعي
input int MaxLearningHistory = 500;          // أقصى تاريخ للتعلم

// 🔧 إعدادات التحليل المتقدم
input double MinDistanceFromLowPips = 50;
input double MinDistanceFromHighPips = 50;
input int TrendConfirmationPeriod = 20;
input double SafetyBufferPips = 30;
input double MinEMAGapPoints = 20;
input double MinATRPoints = 15;
input double ATR_Multiplier = 1.5;
input double MinStopLossPips = 10;
input double MaxStopLossPips = 200;

enum ENUM_SL_METHOD { SL_LAST_SWING = 0, SL_OB_ZONE = 1 };
input ENUM_SL_METHOD StopLossMethod = SL_LAST_SWING;

// 📊 متغيرات المؤشرات
int maFastHandle, maSlowHandle, rsiHandle, atrHandle;
ulong positionTicket = 0;
double lotRemaining = 0.0;
bool positionOpen = false;
datetime lastTradeTime = 0;
double lastAdjustedSL = 0.0;
double entryPrice = 0.0;
int positionDirection = 0;

// 🤖 متغيرات الذكاء الاصطناعي المتقدم
struct AITradeRecord {
    datetime time;
    int direction;
    double entryPrice;
    double exitPrice;
    double profit;
    double rsi;
    double maFast;
    double maSlow;
    double atr;
    bool wasCorrect;
    string pattern;
    double confidence;
};

AITradeRecord aiHistory[];           // تاريخ الصفقات للتعلم
double neuralWeights[10];           // أوزان الشبكة العصبية
double aiConfidenceScore = 0.0;     // نقاط الثقة الحالية
int totalTrades = 0;                // إجمالي الصفقات
int successfulTrades = 0;           // الصفقات الناجحة
double adaptiveThreshold = 0.5;     // العتبة التكيفية
bool aiLearningMode = true;         // وضع التعلم
datetime lastAIUpdate = 0;          // آخر تحديث للذكاء الاصطناعي

// متغيرات قابلة للتعديل (نسخ من input)
double dynamicConfidenceThreshold = 0.75;  // نسخة قابلة للتعديل
double dynamicAdaptationRate = 0.1;        // نسخة قابلة للتعديل

// 📈 متغيرات تحليل الأنماط المتقدم
struct MarketPattern {
    string name;
    double reliability;
    int occurrences;
    double avgProfit;
    bool isActive;
};

MarketPattern detectedPatterns[20];  // الأنماط المكتشفة
int patternCount = 0;               // عدد الأنماط

// 🌊 متغيرات Elliott Wave
struct ElliottWave {
    double wave1_start, wave1_end;
    double wave2_start, wave2_end;
    double wave3_start, wave3_end;
    double wave4_start, wave4_end;
    double wave5_start, wave5_end;
    bool isComplete;
    int currentWave;
    double confidence;
};

ElliottWave currentElliottWave;

// 📐 متغيرات فيبوناتشي
struct FibonacciLevels {
    double level_0;      // 0%
    double level_236;    // 23.6%
    double level_382;    // 38.2%
    double level_500;    // 50%
    double level_618;    // 61.8%
    double level_786;    // 78.6%
    double level_100;    // 100%
    bool isValid;
    datetime calculatedTime;
};

FibonacciLevels fibLevels;

// ✅ متغيرات التحليل المستمر
bool analysisReady = false;
int nextDirection = 0;
datetime lastAnalysisTime = 0;
int analysisConfirmCount = 0;
int requiredConfirms = 8;
datetime analysisStartTime = 0;
bool initialAnalysisComplete = false;
bool botJustStarted = true;
bool deepAnalysisMode = true;
int analysisPhase = 0;
datetime lastDeepAnalysis = 0;
int analysisStepCounter = 0;

int OnInit()
{
    // تهيئة المؤشرات
    maFastHandle = iMA(_Symbol, TimeFrame, 10, 0, MODE_EMA, PRICE_CLOSE);
    maSlowHandle = iMA(_Symbol, TimeFrame, 20, 0, MODE_EMA, PRICE_CLOSE);
    rsiHandle = iRSI(_Symbol, TimeFrame, 14, PRICE_CLOSE);
    atrHandle = iATR(_Symbol, TimeFrame, 14);

    if (maFastHandle == INVALID_HANDLE || maSlowHandle == INVALID_HANDLE ||
        rsiHandle == INVALID_HANDLE || atrHandle == INVALID_HANDLE)
        return INIT_FAILED;

    // 🤖 تهيئة الذكاء الاصطناعي
    InitializeAI();
    
    Print("🚀 ═══════════════════════════════════════════════════════");
    Print("🤖 تم تحميل AHMAD AI SUPREME - أعلى مستوى ذكاء اصطناعي!");
    Print("🧠 الميزات: شبكة عصبية + تعلم آلي + Elliott Wave + فيبوناتشي");
    Print("✨ نظام تكيف ذكي + كشف أنماط متقدم + تحسين ذاتي");
    Print("🚀 ═══════════════════════════════════════════════════════");
    
    return INIT_SUCCEEDED;
}

void OnDeinit(const int reason)
{
    // حفظ بيانات التعلم
    SaveAILearningData();
    
    IndicatorRelease(maFastHandle);
    IndicatorRelease(maSlowHandle);
    IndicatorRelease(rsiHandle);
    IndicatorRelease(atrHandle);
    
    Print("💾 تم حفظ بيانات التعلم - الذكاء الاصطناعي محفوظ للجلسة القادمة");
}

// 🤖 تهيئة نظام الذكاء الاصطناعي
void InitializeAI()
{
    Print("🤖 تهيئة نظام الذكاء الاصطناعي المتقدم...");

    // تهيئة المتغيرات الديناميكية
    dynamicConfidenceThreshold = AI_ConfidenceThreshold;
    dynamicAdaptationRate = AI_AdaptationRate;

    // تهيئة أوزان الشبكة العصبية بقيم عشوائية
    for(int i = 0; i < 10; i++)
    {
        neuralWeights[i] = (MathRand() / 32767.0) * 2.0 - 1.0; // قيم بين -1 و 1
    }
    
    // تهيئة الأنماط
    InitializePatterns();
    
    // تحميل بيانات التعلم السابقة (إن وجدت)
    LoadAILearningData();
    
    // تهيئة Elliott Wave
    InitializeElliottWave();
    
    Print("✅ تم تهيئة الذكاء الاصطناعي بنجاح");
    Print("🧠 الشبكة العصبية: ", (EnableNeuralNetwork ? "مفعلة ✅" : "معطلة ❌"));
    Print("📊 كشف الأنماط: ", (EnablePatternRecognition ? "مفعل ✅" : "معطل ❌"));
    Print("🌊 Elliott Wave: ", (EnableElliottWave ? "مفعل ✅" : "معطل ❌"));
    Print("📐 فيبوناتشي: ", (EnableFibonacci ? "مفعل ✅" : "معطل ❌"));
}

// 🧠 تهيئة الأنماط المعروفة
void InitializePatterns()
{
    patternCount = 0;
    
    // إضافة الأنماط الأساسية
    AddPattern("Double Bottom", 0.75, 0, 0.0);
    AddPattern("Double Top", 0.75, 0, 0.0);
    AddPattern("Head and Shoulders", 0.80, 0, 0.0);
    AddPattern("Inverse H&S", 0.80, 0, 0.0);
    AddPattern("Triangle", 0.65, 0, 0.0);
    AddPattern("Flag", 0.70, 0, 0.0);
    AddPattern("Pennant", 0.70, 0, 0.0);
    AddPattern("Wedge", 0.75, 0, 0.0);
    AddPattern("Channel", 0.60, 0, 0.0);
    AddPattern("Divergence", 0.85, 0, 0.0);
    
    Print("📊 تم تهيئة ", patternCount, " نمط للتحليل");
}

void AddPattern(string name, double reliability, int occurrences, double avgProfit)
{
    if(patternCount < 20)
    {
        detectedPatterns[patternCount].name = name;
        detectedPatterns[patternCount].reliability = reliability;
        detectedPatterns[patternCount].occurrences = occurrences;
        detectedPatterns[patternCount].avgProfit = avgProfit;
        detectedPatterns[patternCount].isActive = false;
        patternCount++;
    }
}

// 🌊 تهيئة Elliott Wave
void InitializeElliottWave()
{
    currentElliottWave.wave1_start = 0;
    currentElliottWave.wave1_end = 0;
    currentElliottWave.wave2_start = 0;
    currentElliottWave.wave2_end = 0;
    currentElliottWave.wave3_start = 0;
    currentElliottWave.wave3_end = 0;
    currentElliottWave.wave4_start = 0;
    currentElliottWave.wave4_end = 0;
    currentElliottWave.wave5_start = 0;
    currentElliottWave.wave5_end = 0;
    currentElliottWave.isComplete = false;
    currentElliottWave.currentWave = 0;
    currentElliottWave.confidence = 0.0;

    Print("🌊 تم تهيئة نظام Elliott Wave");
}

// 💾 حفظ بيانات التعلم
void SaveAILearningData()
{
    Print("💾 حفظ بيانات التعلم للذكاء الاصطناعي...");
    // هنا يمكن حفظ البيانات في ملف
    Print("✅ تم حفظ ", ArraySize(aiHistory), " سجل تداول للتعلم");
}

// 📂 تحميل بيانات التعلم
void LoadAILearningData()
{
    Print("📂 تحميل بيانات التعلم السابقة...");
    // هنا يمكن تحميل البيانات من ملف
    Print("✅ تم تحميل بيانات التعلم السابقة");
}

// 🤖 الشبكة العصبية البسيطة
double NeuralNetworkPredict(double &inputs[])
{
    if(!EnableNeuralNetwork) return 0.5;

    double sum = 0.0;
    int inputSize = MathMin(ArraySize(inputs), 10);

    // حساب المجموع المرجح
    for(int i = 0; i < inputSize; i++)
    {
        sum += inputs[i] * neuralWeights[i];
    }

    // تطبيق دالة التفعيل (sigmoid)
    double result = 1.0 / (1.0 + MathExp(-sum));

    return result;
}

// 🧠 تحديث أوزان الشبكة العصبية (التعلم)
void UpdateNeuralWeights(double &inputs[], double expectedOutput, double actualOutput)
{
    if(!EnableNeuralNetwork) return;

    double error = expectedOutput - actualOutput;
    int inputSize = MathMin(ArraySize(inputs), 10);

    // تحديث الأوزان باستخدام gradient descent
    for(int i = 0; i < inputSize; i++)
    {
        neuralWeights[i] += dynamicAdaptationRate * error * inputs[i];

        // تقييد الأوزان
        if(neuralWeights[i] > 2.0) neuralWeights[i] = 2.0;
        if(neuralWeights[i] < -2.0) neuralWeights[i] = -2.0;
    }
}

// 📊 كشف الأنماط المتقدم
string DetectMarketPatterns()
{
    if(!EnablePatternRecognition) return "None";

    string detectedPattern = "None";
    double maxConfidence = 0.0;

    // فحص Double Bottom
    if(IsDoubleBottom())
    {
        detectedPattern = "Double Bottom";
        maxConfidence = 0.75;
        UpdatePatternStats("Double Bottom");
    }

    // فحص Double Top
    if(IsDoubleTop())
    {
        if(0.75 > maxConfidence)
        {
            detectedPattern = "Double Top";
            maxConfidence = 0.75;
        }
        UpdatePatternStats("Double Top");
    }

    // فحص Head and Shoulders
    if(IsHeadAndShoulders())
    {
        if(0.80 > maxConfidence)
        {
            detectedPattern = "Head and Shoulders";
            maxConfidence = 0.80;
        }
        UpdatePatternStats("Head and Shoulders");
    }

    // فحص الدايفرجنس
    if(IsDivergence())
    {
        if(0.85 > maxConfidence)
        {
            detectedPattern = "Divergence";
            maxConfidence = 0.85;
        }
        UpdatePatternStats("Divergence");
    }

    if(StringCompare(detectedPattern, "None") != 0)
    {
        Print("📊 تم كشف نمط: ", detectedPattern, " | الثقة: ", DoubleToString(maxConfidence * 100, 1), "%");
    }

    return detectedPattern;
}

// 🔍 فحص Double Bottom
bool IsDoubleBottom()
{
    double lows[10];
    for(int i = 0; i < 10; i++)
    {
        lows[i] = iLow(_Symbol, TimeFrame, i + 1);
    }

    // البحث عن قاعين متقاربين
    for(int i = 2; i < 8; i++)
    {
        for(int j = i + 2; j < 10; j++)
        {
            double diff = MathAbs(lows[i] - lows[j]) / _Point;
            if(diff < 20) // قاعان متقاربان (أقل من 20 نقطة)
            {
                // فحص وجود قمة بينهما
                bool hasHighBetween = false;
                for(int k = i + 1; k < j; k++)
                {
                    if(iHigh(_Symbol, TimeFrame, k + 1) > MathMax(lows[i], lows[j]) + 30 * _Point)
                    {
                        hasHighBetween = true;
                        break;
                    }
                }

                if(hasHighBetween)
                {
                    return true;
                }
            }
        }
    }

    return false;
}

// 🔍 فحص Double Top
bool IsDoubleTop()
{
    double highs[10];
    for(int i = 0; i < 10; i++)
    {
        highs[i] = iHigh(_Symbol, TimeFrame, i + 1);
    }

    // البحث عن قمتين متقاربتين
    for(int i = 2; i < 8; i++)
    {
        for(int j = i + 2; j < 10; j++)
        {
            double diff = MathAbs(highs[i] - highs[j]) / _Point;
            if(diff < 20) // قمتان متقاربتان
            {
                // فحص وجود قاع بينهما
                bool hasLowBetween = false;
                for(int k = i + 1; k < j; k++)
                {
                    if(iLow(_Symbol, TimeFrame, k + 1) < MathMin(highs[i], highs[j]) - 30 * _Point)
                    {
                        hasLowBetween = true;
                        break;
                    }
                }

                if(hasLowBetween)
                {
                    return true;
                }
            }
        }
    }

    return false;
}

// 🔍 فحص Head and Shoulders
bool IsHeadAndShoulders()
{
    double highs[15];
    for(int i = 0; i < 15; i++)
    {
        highs[i] = iHigh(_Symbol, TimeFrame, i + 1);
    }

    // البحث عن نمط الرأس والكتفين
    for(int head = 5; head < 10; head++)
    {
        double headPrice = highs[head];

        // البحث عن الكتف الأيسر
        for(int leftShoulder = 1; leftShoulder < head - 2; leftShoulder++)
        {
            double leftPrice = highs[leftShoulder];

            // البحث عن الكتف الأيمن
            for(int rightShoulder = head + 2; rightShoulder < 14; rightShoulder++)
            {
                double rightPrice = highs[rightShoulder];

                // فحص الشروط
                bool headHigher = headPrice > leftPrice + 20 * _Point && headPrice > rightPrice + 20 * _Point;
                bool shouldersEqual = MathAbs(leftPrice - rightPrice) < 15 * _Point;

                if(headHigher && shouldersEqual)
                {
                    return true;
                }
            }
        }
    }

    return false;
}

// 🔍 فحص الدايفرجنس
bool IsDivergence()
{
    double rsiBuffer[5], priceBuffer[5];

    if(CopyBuffer(rsiHandle, 0, 0, 5, rsiBuffer) <= 0) return false;

    for(int i = 0; i < 5; i++)
    {
        priceBuffer[i] = iClose(_Symbol, TimeFrame, i);
    }

    // فحص الدايفرجنس الصاعد (السعر ينخفض، RSI يرتفع)
    bool priceFalling = priceBuffer[0] < priceBuffer[4];
    bool rsiRising = rsiBuffer[0] > rsiBuffer[4];

    // فحص الدايفرجنس الهابط (السعر يرتفع، RSI ينخفض)
    bool priceRising = priceBuffer[0] > priceBuffer[4];
    bool rsiFalling = rsiBuffer[0] < rsiBuffer[4];

    return (priceFalling && rsiRising) || (priceRising && rsiFalling);
}

// 📊 تحديث إحصائيات النمط
void UpdatePatternStats(string patternName)
{
    for(int i = 0; i < patternCount; i++)
    {
        if(StringCompare(detectedPatterns[i].name, patternName) == 0)
        {
            detectedPatterns[i].occurrences++;
            detectedPatterns[i].isActive = true;
            break;
        }
    }
}

// 🌊 تحليل Elliott Wave
void AnalyzeElliottWave()
{
    if(!EnableElliottWave) return;

    Print("🌊 تحليل Elliott Wave...");

    // تحديد الموجات الحالية
    IdentifyElliottWaves();

    // حساب الثقة في النمط
    CalculateElliottWaveConfidence();

    if(currentElliottWave.confidence > 0.7)
    {
        Print("🌊 Elliott Wave مؤكد | الموجة الحالية: ", currentElliottWave.currentWave,
              " | الثقة: ", DoubleToString(currentElliottWave.confidence * 100, 1), "%");
    }
}

// 🌊 تحديد موجات Elliott
void IdentifyElliottWaves()
{
    // خوارزمية مبسطة لتحديد الموجات
    double highs[50], lows[50];

    for(int i = 0; i < 50; i++)
    {
        highs[i] = iHigh(_Symbol, TimeFrame, i + 1);
        lows[i] = iLow(_Symbol, TimeFrame, i + 1);
    }

    // تحديد القمم والقيعان المهمة
    int peaks[10], valleys[10];
    int peakCount = 0, valleyCount = 0;

    // البحث عن القمم
    for(int i = 2; i < 48; i++)
    {
        if(highs[i] > highs[i-1] && highs[i] > highs[i+1] &&
           highs[i] > highs[i-2] && highs[i] > highs[i+2])
        {
            if(peakCount < 10)
            {
                peaks[peakCount] = i;
                peakCount++;
            }
        }
    }

    // البحث عن القيعان
    for(int i = 2; i < 48; i++)
    {
        if(lows[i] < lows[i-1] && lows[i] < lows[i+1] &&
           lows[i] < lows[i-2] && lows[i] < lows[i+2])
        {
            if(valleyCount < 10)
            {
                valleys[valleyCount] = i;
                valleyCount++;
            }
        }
    }

    // تحليل الموجات بناءً على القمم والقيعان
    if(peakCount >= 3 && valleyCount >= 2)
    {
        currentElliottWave.currentWave = DetermineCurrentWave(peaks, valleys, peakCount, valleyCount);
    }
}

// 🌊 تحديد الموجة الحالية
int DetermineCurrentWave(int &peaks[], int &valleys[], int peakCount, int valleyCount)
{
    // خوارزمية مبسطة لتحديد الموجة
    // الموجة 1: صاعدة
    // الموجة 2: تصحيح هابط
    // الموجة 3: صاعدة قوية (أطول موجة)
    // الموجة 4: تصحيح هابط أصغر
    // الموجة 5: صاعدة نهائية

    if(peakCount >= 2 && valleyCount >= 1)
    {
        double currentPrice = iClose(_Symbol, TimeFrame, 0);
        double lastPeak = iHigh(_Symbol, TimeFrame, peaks[0] + 1);
        double lastValley = iLow(_Symbol, TimeFrame, valleys[0] + 1);

        if(currentPrice > lastPeak)
            return 3; // موجة صاعدة قوية
        else if(currentPrice < lastValley)
            return 2; // موجة تصحيح
        else
            return 1; // موجة أولى
    }

    return 0; // غير محدد
}

// 🌊 حساب ثقة Elliott Wave
void CalculateElliottWaveConfidence()
{
    double confidence = 0.0;

    // عوامل الثقة
    if(currentElliottWave.currentWave > 0) confidence += 0.2;

    // فحص نسب فيبوناتشي
    if(EnableFibonacci && fibLevels.isValid)
    {
        confidence += 0.3;
    }

    // فحص قوة الترند
    double maFastBuffer[], maSlowBuffer[];
    if(CopyBuffer(maFastHandle, 0, 0, 1, maFastBuffer) > 0 &&
       CopyBuffer(maSlowHandle, 0, 0, 1, maSlowBuffer) > 0)
    {
        if(MathAbs(maFastBuffer[0] - maSlowBuffer[0]) > 10 * _Point)
            confidence += 0.2;
    }

    // فحص حجم التداول (إذا متوفر)
    confidence += 0.3; // افتراضي

    currentElliottWave.confidence = confidence;
}

// 📐 حساب مستويات فيبوناتشي
void CalculateFibonacciLevels()
{
    if(!EnableFibonacci) return;

    // البحث عن أعلى وأقل نقطة في آخر 50 شمعة
    double highest = iHigh(_Symbol, TimeFrame, 1);
    double lowest = iLow(_Symbol, TimeFrame, 1);

    for(int i = 2; i <= 50; i++)
    {
        double high = iHigh(_Symbol, TimeFrame, i);
        double low = iLow(_Symbol, TimeFrame, i);

        if(high > highest) highest = high;
        if(low < lowest) lowest = low;
    }

    double range = highest - lowest;

    // حساب مستويات فيبوناتشي
    fibLevels.level_0 = highest;
    fibLevels.level_236 = highest - (range * 0.236);
    fibLevels.level_382 = highest - (range * 0.382);
    fibLevels.level_500 = highest - (range * 0.500);
    fibLevels.level_618 = highest - (range * 0.618);
    fibLevels.level_786 = highest - (range * 0.786);
    fibLevels.level_100 = lowest;
    fibLevels.isValid = true;
    fibLevels.calculatedTime = TimeCurrent();

    Print("📐 مستويات فيبوناتشي محدثة:");
    Print("   0%: ", DoubleToString(fibLevels.level_0, _Digits));
    Print("   23.6%: ", DoubleToString(fibLevels.level_236, _Digits));
    Print("   38.2%: ", DoubleToString(fibLevels.level_382, _Digits));
    Print("   50%: ", DoubleToString(fibLevels.level_500, _Digits));
    Print("   61.8%: ", DoubleToString(fibLevels.level_618, _Digits));
    Print("   78.6%: ", DoubleToString(fibLevels.level_786, _Digits));
    Print("   100%: ", DoubleToString(fibLevels.level_100, _Digits));
}

// 📐 فحص القرب من مستويات فيبوناتشي
bool IsNearFibonacciLevel(double price, double &nearestLevel)
{
    if(!fibLevels.isValid) return false;

    double tolerance = 5 * _Point; // تسامح 5 نقاط
    double levels[] = {
        fibLevels.level_0, fibLevels.level_236, fibLevels.level_382,
        fibLevels.level_500, fibLevels.level_618, fibLevels.level_786, fibLevels.level_100
    };

    for(int i = 0; i < 7; i++)
    {
        if(MathAbs(price - levels[i]) <= tolerance)
        {
            nearestLevel = levels[i];
            return true;
        }
    }

    return false;
}

// 🤖 التحليل الشامل بالذكاء الاصطناعي
int AIAnalyzeMarket()
{
    Print("🤖 بدء التحليل الشامل بالذكاء الاصطناعي...");

    // جمع البيانات للتحليل
    double inputs[10];
    CollectMarketData(inputs);

    // تحليل الأنماط
    string detectedPattern = DetectMarketPatterns();

    // تحليل Elliott Wave
    AnalyzeElliottWave();

    // حساب فيبوناتشي
    CalculateFibonacciLevels();

    // استخدام الشبكة العصبية للتنبؤ
    double neuralPrediction = NeuralNetworkPredict(inputs);

    // حساب نقاط الثقة الإجمالية
    double totalConfidence = CalculateOverallConfidence(detectedPattern, neuralPrediction);

    // اتخاذ القرار النهائي
    int decision = MakeAIDecision(totalConfidence, detectedPattern, neuralPrediction);

    Print("🤖 نتائج التحليل الذكي:");
    Print("   📊 النمط المكتشف: ", detectedPattern);
    Print("   🧠 تنبؤ الشبكة العصبية: ", DoubleToString(neuralPrediction, 3));
    Print("   🌊 Elliott Wave: موجة ", currentElliottWave.currentWave, " (ثقة: ", DoubleToString(currentElliottWave.confidence * 100, 1), "%)");
    Print("   📐 فيبوناتشي: ", (fibLevels.isValid ? "محسوب ✅" : "غير متوفر ❌"));
    Print("   🎯 الثقة الإجمالية: ", DoubleToString(totalConfidence * 100, 1), "%");
    Print("   ⚡ القرار النهائي: ", (decision == 1 ? "شراء 🟢" : decision == -1 ? "بيع 🔴" : "انتظار ⚪"));

    return decision;
}

// 📊 جمع بيانات السوق للتحليل
void CollectMarketData(double &inputs[])
{
    double maFastBuffer[], maSlowBuffer[], rsiBuffer[], atrBuffer[];

    if(CopyBuffer(maFastHandle, 0, 0, 1, maFastBuffer) > 0 &&
       CopyBuffer(maSlowHandle, 0, 0, 1, maSlowBuffer) > 0 &&
       CopyBuffer(rsiHandle, 0, 0, 1, rsiBuffer) > 0 &&
       CopyBuffer(atrHandle, 0, 0, 1, atrBuffer) > 0)
    {
        double currentPrice = iClose(_Symbol, TimeFrame, 0);

        // تطبيع البيانات للشبكة العصبية
        inputs[0] = (maFastBuffer[0] - maSlowBuffer[0]) / (atrBuffer[0] * 100); // فرق المتوسطات
        inputs[1] = (rsiBuffer[0] - 50) / 50; // RSI منتصف
        inputs[2] = atrBuffer[0] / currentPrice; // التقلبات النسبية
        inputs[3] = GetDistanceFromNearestLow() / 100; // المسافة من القاع
        inputs[4] = GetDistanceFromNearestHigh() / 100; // المسافة من القمة
        inputs[5] = (currentElliottWave.confidence - 0.5) * 2; // ثقة Elliott Wave
        inputs[6] = (double)currentElliottWave.currentWave / 5.0; // الموجة الحالية

        // فحص القرب من فيبوناتشي
        double nearestFib;
        inputs[7] = IsNearFibonacciLevel(currentPrice, nearestFib) ? 1.0 : 0.0;

        // اتجاه السعر
        double prevPrice = iClose(_Symbol, TimeFrame, 1);
        inputs[8] = (currentPrice > prevPrice) ? 1.0 : -1.0;

        // قوة الحجم (افتراضي)
        inputs[9] = 0.5;
    }
}

// 🎯 حساب الثقة الإجمالية
double CalculateOverallConfidence(string pattern, double neuralPrediction)
{
    double confidence = 0.0;

    // ثقة الشبكة العصبية (40%)
    confidence += MathAbs(neuralPrediction - 0.5) * 2 * 0.4;

    // ثقة الأنماط (30%)
    if(StringCompare(pattern, "None") != 0)
    {
        for(int i = 0; i < patternCount; i++)
        {
            if(StringCompare(detectedPatterns[i].name, pattern) == 0)
            {
                confidence += detectedPatterns[i].reliability * 0.3;
                break;
            }
        }
    }

    // ثقة Elliott Wave (20%)
    confidence += currentElliottWave.confidence * 0.2;

    // ثقة فيبوناتشي (10%)
    if(fibLevels.isValid)
    {
        double currentPrice = iClose(_Symbol, TimeFrame, 0);
        double nearestFib;
        if(IsNearFibonacciLevel(currentPrice, nearestFib))
            confidence += 0.1;
    }

    return MathMin(confidence, 1.0);
}

// ⚡ اتخاذ القرار النهائي بالذكاء الاصطناعي
int MakeAIDecision(double confidence, string pattern, double neuralPrediction)
{
    // فحص حد الثقة
    if(confidence < dynamicConfidenceThreshold)
    {
        Print("❌ الثقة منخفضة (", DoubleToString(confidence * 100, 1), "%) - لا قرار");
        return 0;
    }

    // تحليل الاتجاه من الشبكة العصبية
    int neuralDirection = (neuralPrediction > 0.6) ? 1 : (neuralPrediction < 0.4) ? -1 : 0;

    // تحليل الاتجاه من الأنماط
    int patternDirection = GetPatternDirection(pattern);

    // تحليل الاتجاه من Elliott Wave
    int elliottDirection = GetElliottWaveDirection();

    // التصويت النهائي
    int votes = neuralDirection + patternDirection + elliottDirection;

    if(votes >= 2) return 1;  // شراء
    if(votes <= -2) return -1; // بيع

    return 0; // انتظار
}

// 📊 تحديد اتجاه النمط
int GetPatternDirection(string pattern)
{
    if(StringCompare(pattern, "Double Bottom") == 0) return 1;
    if(StringCompare(pattern, "Inverse H&S") == 0) return 1;
    if(StringCompare(pattern, "Double Top") == 0) return -1;
    if(StringCompare(pattern, "Head and Shoulders") == 0) return -1;

    // للأنماط الأخرى، فحص السياق
    double rsiBuffer[];
    if(CopyBuffer(rsiHandle, 0, 0, 1, rsiBuffer) > 0)
    {
        if(StringCompare(pattern, "Divergence") == 0)
        {
            return (rsiBuffer[0] < 30) ? 1 : (rsiBuffer[0] > 70) ? -1 : 0;
        }
    }

    return 0;
}

// 🌊 تحديد اتجاه Elliott Wave
int GetElliottWaveDirection()
{
    if(currentElliottWave.confidence < 0.5) return 0;

    switch(currentElliottWave.currentWave)
    {
        case 1: return 1;  // موجة 1 صاعدة
        case 2: return -1; // موجة 2 تصحيح هابط
        case 3: return 1;  // موجة 3 صاعدة قوية
        case 4: return -1; // موجة 4 تصحيح هابط
        case 5: return 1;  // موجة 5 صاعدة نهائية
        default: return 0;
    }
}

// 🧠 تعلم من نتائج الصفقة
void LearnFromTrade(int direction, double entryPriceLocal, double exitPrice, bool wasSuccessful)
{
    if(!EnableSelfOptimization) return;

    Print("🧠 التعلم من الصفقة...");

    // إضافة السجل للتاريخ
    int historySize = ArraySize(aiHistory);
    if(historySize >= MaxLearningHistory)
    {
        // إزالة أقدم سجل
        for(int i = 0; i < historySize - 1; i++)
        {
            aiHistory[i] = aiHistory[i + 1];
        }
        historySize--;
    }

    ArrayResize(aiHistory, historySize + 1);

    // حفظ بيانات الصفقة
    aiHistory[historySize].time = TimeCurrent();
    aiHistory[historySize].direction = direction;
    aiHistory[historySize].entryPrice = entryPriceLocal;
    aiHistory[historySize].exitPrice = exitPrice;
    aiHistory[historySize].profit = (exitPrice - entryPriceLocal) * direction;
    aiHistory[historySize].wasCorrect = wasSuccessful;
    aiHistory[historySize].confidence = aiConfidenceScore;

    // جمع بيانات السوق وقت الدخول
    double inputs[10];
    CollectMarketData(inputs);

    // تحديث الشبكة العصبية
    double expectedOutput = wasSuccessful ? 1.0 : 0.0;
    double actualOutput = NeuralNetworkPredict(inputs);
    UpdateNeuralWeights(inputs, expectedOutput, actualOutput);

    // تحديث الإحصائيات
    totalTrades++;
    if(wasSuccessful) successfulTrades++;

    // تحديث العتبة التكيفية
    double successRate = (double)successfulTrades / totalTrades;
    adaptiveThreshold = 0.3 + (successRate * 0.4); // بين 0.3 و 0.7

    Print("📊 إحصائيات التعلم:");
    Print("   📈 إجمالي الصفقات: ", totalTrades);
    Print("   ✅ الصفقات الناجحة: ", successfulTrades);
    Print("   📊 معدل النجاح: ", DoubleToString(successRate * 100, 1), "%");
    Print("   🎯 العتبة التكيفية: ", DoubleToString(adaptiveThreshold, 3));

    // تحسين ذاتي للمعايير
    if(totalTrades >= AI_LearningPeriod && totalTrades % 10 == 0)
    {
        OptimizeParameters();
    }
}

// ⚙️ تحسين المعايير تلقائياً
void OptimizeParameters()
{
    Print("⚙️ تحسين المعايير تلقائياً...");

    double successRate = (double)successfulTrades / totalTrades;

    // تحسين حد الثقة
    if(successRate > 0.7)
    {
        dynamicConfidenceThreshold = MathMax(0.5, dynamicConfidenceThreshold - 0.05);
        Print("📈 تقليل حد الثقة إلى: ", DoubleToString(dynamicConfidenceThreshold, 2));
    }
    else if(successRate < 0.5)
    {
        dynamicConfidenceThreshold = MathMin(0.9, dynamicConfidenceThreshold + 0.05);
        Print("📉 زيادة حد الثقة إلى: ", DoubleToString(dynamicConfidenceThreshold, 2));
    }

    // تحسين معدل التكيف
    if(successRate > 0.6)
    {
        dynamicAdaptationRate = MathMin(0.5, dynamicAdaptationRate + 0.01);
    }
    else
    {
        dynamicAdaptationRate = MathMax(0.01, dynamicAdaptationRate - 0.01);
    }

    Print("✅ تم تحسين المعايير تلقائياً");
}

// 🔧 الدوال المساعدة من الكود الأصلي
bool IsCooldownFinished() { return (TimeCurrent() - lastTradeTime) >= (CooldownMinutes * 60); }

double GetATRPips()
{
    double atrBuffer[];
    if (CopyBuffer(atrHandle, 0, 0, 1, atrBuffer) <= 0) return 0;
    return atrBuffer[0] / _Point;
}

double GetDistanceFromNearestLow()
{
    double nearestLow = iLow(_Symbol, TimeFrame, 0);

    for(int i = 1; i <= 50; i++)
    {
        double currentLow = iLow(_Symbol, TimeFrame, i);
        if(currentLow < nearestLow)
            nearestLow = currentLow;
    }

    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double distancePips = (currentPrice - nearestLow) / _Point;

    return distancePips;
}

double GetDistanceFromNearestHigh()
{
    double nearestHigh = iHigh(_Symbol, TimeFrame, 0);

    for(int i = 1; i <= 50; i++)
    {
        double currentHigh = iHigh(_Symbol, TimeFrame, i);
        if(currentHigh > nearestHigh)
            nearestHigh = currentHigh;
    }

    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double distancePips = (nearestHigh - currentPrice) / _Point;

    return distancePips;
}

bool IsRealTrendChange(bool checkForUptrend)
{
    int confirmCount = 0;
    int requiredTrendConfirms = TrendConfirmationPeriod / 4;

    if(checkForUptrend)
    {
        for(int i = 1; i <= TrendConfirmationPeriod; i += 4)
        {
            double low1 = iLow(_Symbol, TimeFrame, i);
            double low2 = iLow(_Symbol, TimeFrame, i + 4);

            if(low1 > low2) confirmCount++;
        }
    }
    else
    {
        for(int i = 1; i <= TrendConfirmationPeriod; i += 4)
        {
            double high1 = iHigh(_Symbol, TimeFrame, i);
            double high2 = iHigh(_Symbol, TimeFrame, i + 4);

            if(high1 < high2) confirmCount++;
        }
    }

    return confirmCount >= requiredTrendConfirms;
}

// 🔧 دالة IsReversalToBuy المحسنة بالذكاء الاصطناعي
bool IsReversalToBuy()
{
    Print("🤖 تحليل إشارة الشراء بالذكاء الاصطناعي...");

    // فحص المسافة من أقرب قمة
    double distanceFromHigh = GetDistanceFromNearestHigh();
    if(distanceFromHigh < MinDistanceFromHighPips + SafetyBufferPips)
    {
        Print("❌ رفض الشراء: قريب من القمة (", DoubleToString(distanceFromHigh, 1), " نقطة)");
        return false;
    }

    // استخدام التحليل الذكي
    int aiDecision = AIAnalyzeMarket();
    if(aiDecision != 1)
    {
        Print("❌ الذكاء الاصطناعي لا ينصح بالشراء");
        return false;
    }

    // فحص الترند الحقيقي
    if(!IsRealTrendChange(false))
    {
        Print("❌ لا يوجد ترند هابط حقيقي سابق");
        return false;
    }

    // فحص القرب من مستويات فيبوناتشي الداعمة
    double currentPrice = iClose(_Symbol, TimeFrame, 0);
    double nearestFib;
    bool nearFibSupport = IsNearFibonacciLevel(currentPrice, nearestFib);

    if(EnableFibonacci && nearFibSupport)
    {
        // فحص إذا كان المستوى داعم
        if(nearestFib == fibLevels.level_618 || nearestFib == fibLevels.level_786)
        {
            Print("✅ قريب من مستوى فيبوناتشي داعم: ", DoubleToString(nearestFib, _Digits));
        }
    }

    Print("✅ الذكاء الاصطناعي يؤكد إشارة الشراء");
    return true;
}

// 🔧 دالة IsReversalToSell المحسنة بالذكاء الاصطناعي
bool IsReversalToSell()
{
    Print("🤖 تحليل إشارة البيع بالذكاء الاصطناعي...");

    // فحص المسافة من أقرب قاع
    double distanceFromLow = GetDistanceFromNearestLow();
    if(distanceFromLow < MinDistanceFromLowPips + SafetyBufferPips)
    {
        Print("❌ رفض البيع: قريب من القاع (", DoubleToString(distanceFromLow, 1), " نقطة)");
        return false;
    }

    // استخدام التحليل الذكي
    int aiDecision = AIAnalyzeMarket();
    if(aiDecision != -1)
    {
        Print("❌ الذكاء الاصطناعي لا ينصح بالبيع");
        return false;
    }

    // فحص الترند الحقيقي
    if(!IsRealTrendChange(true))
    {
        Print("❌ لا يوجد ترند صاعد حقيقي سابق");
        return false;
    }

    // فحص القرب من مستويات فيبوناتشي المقاومة
    double currentPrice = iClose(_Symbol, TimeFrame, 0);
    double nearestFib;
    bool nearFibResistance = IsNearFibonacciLevel(currentPrice, nearestFib);

    if(EnableFibonacci && nearFibResistance)
    {
        // فحص إذا كان المستوى مقاومة
        if(nearestFib == fibLevels.level_382 || nearestFib == fibLevels.level_500)
        {
            Print("✅ قريب من مستوى فيبوناتشي مقاومة: ", DoubleToString(nearestFib, _Digits));
        }
    }

    Print("✅ الذكاء الاصطناعي يؤكد إشارة البيع");
    return true;
}

// ✅ دالة التحليل الرئيسية
int AnalyzeMarketDirection()
{
    // استخدام الذكاء الاصطناعي للتحليل
    int aiDirection = AIAnalyzeMarket();

    if(aiDirection == 1 && EnableLong && IsReversalToBuy())
    {
        Print("🟢 إشارة شراء مؤكدة بالذكاء الاصطناعي");
        return 1;
    }

    if(aiDirection == -1 && EnableShort && IsReversalToSell())
    {
        Print("🔴 إشارة بيع مؤكدة بالذكاء الاصطناعي");
        return -1;
    }

    return 0;
}

// 🔧 باقي الدوال الأساسية
void SendAlert(string msg) { Alert(msg); }

double GenerateDynamicStopLoss(bool isBuy, double priceEntry)
{
    double atrValue = GetATRPips();
    double baseBuffer = atrValue * ATR_Multiplier;

    if (baseBuffer < MinStopLossPips) baseBuffer = MinStopLossPips;
    if (baseBuffer > MaxStopLossPips) baseBuffer = MaxStopLossPips;

    return (isBuy) ? priceEntry - baseBuffer * _Point : priceEntry + baseBuffer * _Point;
}

void OpenTrade(int direction)
{
    double price = (direction == 1) ? SymbolInfoDouble(_Symbol, SYMBOL_ASK) : SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double sl = GenerateDynamicStopLoss(direction == 1, price);
    bool result = (direction == 1) ? tradeInstance.Buy(FixedLotSize, _Symbol, price, sl, 0)
                                   : tradeInstance.Sell(FixedLotSize, _Symbol, price, sl, 0);

    if (result)
    {
        positionTicket = tradeInstance.ResultOrder();
        positionOpen = true;
        lotRemaining = FixedLotSize;
        lastAdjustedSL = sl;
        lastTradeTime = TimeCurrent();
        entryPrice = price;
        positionDirection = direction;

        string directionText = (direction == 1) ? "شراء" : "بيع";
        SendAlert("🤖 AI تأكيد " + directionText + " - صفقة ذكية!");
        Print("✅ فتح صفقة ", directionText, " بالذكاء الاصطناعي عند: ", price);
    }
    else
    {
        Print("❌ فشل فتح الصفقة. الخطأ: ", GetLastError());
    }
}

void OnTick()
{
    // تحديث الذكاء الاصطناعي كل دقيقة
    static datetime lastAIUpdateLocal = 0;
    if(TimeCurrent() - lastAIUpdateLocal >= 60)
    {
        // تحديث مستويات فيبوناتشي
        if(EnableFibonacci) CalculateFibonacciLevels();

        // تحليل Elliott Wave
        if(EnableElliottWave) AnalyzeElliottWave();

        lastAIUpdateLocal = TimeCurrent();
    }

    if (ManualClose && PositionSelect(_Symbol))
    {
        // تعلم من الإغلاق اليدوي
        double exitPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
        bool wasSuccessful = (positionDirection == 1) ? (exitPrice > entryPrice) : (exitPrice < entryPrice);
        LearnFromTrade(positionDirection, entryPrice, exitPrice, wasSuccessful);

        tradeInstance.PositionClose(_Symbol);
        positionOpen = false;
        lotRemaining = 0;
        lastTradeTime = TimeCurrent();
        return;
    }

    if (!positionOpen && IsCooldownFinished())
    {
        int direction = AnalyzeMarketDirection();
        if(direction != 0)
        {
            OpenTrade(direction);
        }
    }

    // إدارة الصفقة المفتوحة (مبسطة)
    if(positionOpen && PositionSelect(_Symbol))
    {
        double currentPrice = (positionDirection == 1) ? SymbolInfoDouble(_Symbol, SYMBOL_BID) : SymbolInfoDouble(_Symbol, SYMBOL_ASK);
        double profit = (currentPrice - entryPrice) * positionDirection;

        // إغلاق عند الهدف الأول
        if(profit >= TargetPips_TP1 * _Point)
        {
            double exitPrice = currentPrice;
            LearnFromTrade(positionDirection, entryPrice, exitPrice, true);

            tradeInstance.PositionClose(_Symbol);
            positionOpen = false;
            SendAlert("✅ تحقيق الهدف الأول - الذكاء الاصطناعي نجح!");
        }
    }
}
